package cn.fd.customize.mmoProfessions.listeners;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.area.Area;
import cn.fd.customize.mmoProfessions.area.AreaManager;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.*;
import org.bukkit.event.entity.EntityExplodeEvent;
import org.bukkit.event.world.StructureGrowEvent;

/**
 * 区域保护监听器
 * 阻止非玩家原因破坏区域内的方块
 */
public class AreaProtectionListener implements Listener {

    public AreaProtectionListener() {
    }

    /**
     * 阻止实体爆炸破坏区域方块
     */
    @EventHandler()
    public void onEntityExplode(EntityExplodeEvent event) {
        if (event.isCancelled())
            return;

        // 检查爆炸影响的方块
        event.blockList().removeIf(block -> {
            Location location = block.getLocation();
            Area area = Area.findAreaByLocation(location, getAreas());
            if (area != null) {
                return true; // 移除这个方块，防止被破坏
            }
            return false;
        });
    }

    /**
     * 阻止方块爆炸破坏区域方块
     */
    @EventHandler()
    public void onBlockExplode(BlockExplodeEvent event) {
        if (event.isCancelled())
            return;

        // 检查爆炸影响的方块
        event.blockList().removeIf(block -> {
            Location location = block.getLocation();
            Area area = Area.findAreaByLocation(location, getAreas());
            if (area != null) {
                return true; // 移除这个方块，防止被破坏
            }
            return false;
        });
    }

    /**
     * 阻止活塞推动区域内的方块
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPistonExtend(BlockPistonExtendEvent event) {
        if (event.isCancelled())
            return;

        // 检查活塞推动的方块
        for (org.bukkit.block.Block block : event.getBlocks()) {
            Location location = block.getLocation();
            Area area = Area.findAreaByLocation(location, getAreas());
            if (area != null) {
                event.setCancelled(true);
                return;
            }
        }
    }

    /**
     * 阻止活塞拉动区域内的方块
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPistonRetract(BlockPistonRetractEvent event) {
        if (event.isCancelled())
            return;

        // 检查活塞拉动的方块
        for (org.bukkit.block.Block block : event.getBlocks()) {
            Location location = block.getLocation();
            Area area = Area.findAreaByLocation(location, getAreas());
            if (area != null) {
                event.setCancelled(true);
                return;
            }
        }
    }

    /**
     * 阻止方块燃烧破坏区域方块
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBurn(BlockBurnEvent event) {
        if (event.isCancelled())
            return;

        Location location = event.getBlock().getLocation();
        Area area = Area.findAreaByLocation(location, getAreas());
        if (area != null) {
            event.setCancelled(true);
        }
    }

    /**
     * 阻止方块凋零破坏区域方块
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockFade(BlockFadeEvent event) {
        if (event.isCancelled())
            return;

        Location location = event.getBlock().getLocation();
        Area area = Area.findAreaByLocation(location, getAreas());
        if (area != null) {
            event.setCancelled(true);
        }
    }

    /**
     * 阻止液体流动破坏区域方块
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockFromTo(BlockFromToEvent event) {
        if (event.isCancelled())
            return;

        Location toLocation = event.getToBlock().getLocation();
        Area area = Area.findAreaByLocation(toLocation, getAreas());
        if (area != null) {
            event.setCancelled(true);
        }
    }

    private Iterable<Area> getAreas() {
        return MMOProfessions.getAreaManager().getAreas().values();
    }

}
