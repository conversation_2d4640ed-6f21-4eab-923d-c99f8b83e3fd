package cn.fd.customize.mmoProfessions.config;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;

/**
 * 消息管理器 - 负责加载和管理所有插件消息
 */
public class MessageManager {

    private FileConfiguration messageConfig;

    public MessageManager() {
        loadMessages();
    }

    /**
     * 加载消息配置文件
     */
    public void loadMessages() {
        File messageFile = new File(MMOProfessions.getInstance().getDataFolder(), "messages.yml");
        if (!messageFile.exists()) {
            MMOProfessions.getInstance().saveResource("messages.yml", false);
        }

        messageConfig = YamlConfiguration.loadConfiguration(messageFile);
        MMOProfessions.getInstance().getLogger().info("消息配置文件已加载");
    }

    /**
     * 重新加载消息配置
     */
    public void reloadMessages() {
        loadMessages();
    }

    /**
     * 获取消息（支持参数替换）
     */
    public String getMessage(String path, String... variables) {

        // 从配置文件获取消息
        String message = messageConfig.getString(path);

        if (message == null) {
            throw new RuntimeException("Message not found: " + path);
        }

        if (variables != null && variables.length > 0) {
            for (int i = 0; i < variables.length; i++) {
                message = message.replace("{" + i + "}", variables[i]);
            }
        }

        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * 发送消息给CommandSender
     */
    public void sendMessage(CommandSender sender, String path, String... variables) {
        sender.sendMessage(getMessage(path, variables));
    }

    /**
     * 便捷方法：发送消息给玩家（使用Object参数）
     */
    public void sendPlayerMessage(Player player, String path, Object... vars) {
        // 转换Object数组为String数组
        String[] stringVars = new String[vars.length];
        for (int i = 0; i < vars.length; i++) {
            stringVars[i] = String.valueOf(vars[i]);
        }
        sendMessage(player, path, stringVars);
    }

}
