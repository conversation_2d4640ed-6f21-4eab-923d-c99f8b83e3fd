package cn.fd.customize.mmoProfessions.area;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.config.AreaConfigLoader;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import java.util.HashMap;
import java.util.Map;

/**
 * 区域管理器
 */
public class AreaManager {

    private final AreaConfigLoader configLoader;
    private final Map<String, Area> areas;

    public AreaManager() {
        this.configLoader = new AreaConfigLoader();
        this.areas = new HashMap<>();
    }

    /**
     * 加载所有区域配置
     */
    public void loadAreas() {
        configLoader.loadAreas();
        areas.clear();
        areas.putAll(configLoader.getAreas());
    }

    /**
     * 重新加载区域配置
     */
    public void reloadAreas() {
        loadAreas();
        MMOProfessions.getInstance().getLogger().info("区域配置已重新加载");
    }

    /**
     * 处理方块破坏事件 - 严格按照要求的处理逻辑
     *
     * 处理逻辑：
     * 玩家挖掘 valid 方块, 判断 职业、等级、工具，
     * 如果都满足，读条 + 1, 如 1/5，然后取消方块破坏事件，读条记录在 minableDiamond (区域ID) 里。
     * 当区域读条进度满时 (5/5)， 清除 blocks 中的所有方块，加之以 refreshing_blocks 替换，
     * 之后执行 drops (修改掉落物品，执行命令，给予经验，任意一个均可选)。
     * 之后区域进入到刷新阶段，此期间方块无法被破坏，刷新时间 (refresh_interval) 过后，
     * 替换 refreshing_blocks 为 blocks，读条清零。
     *
     * @param player   玩家
     * @param location 方块位置
     * @return 是否应该取消破坏事件
     */
    public boolean handleBlockBreak(Player player, Location location) {
        // 1. 查找包含此位置的区域
        Area area = Area.findAreaByLocation(location, getAreas().values());
        if (area == null) {
            return false; // 不是区域方块，允许正常破坏
        }

        // 2. 检查是否为有效方块 (valid 方块)
        AreaBlock block = area.findBlock(location);
        if (block == null || !block.isValid()) {
            return true; // 是区域方块但不是有效方块，取消破坏但不处理
        }

        // 3. 检查区域状态 - 刷新期间方块无法被破坏
        if (area.getProgress().getStatus() == AreaProgress.AreaStatus.REFRESHING) {
            MMOProfessions.getMessageManager().sendMessage(player, "area.refreshing");
            return true; // 区域正在刷新，取消破坏
        }

        // 4. 判断职业、等级、工具
        ItemStack tool = player.getInventory().getItemInMainHand();
        if (!area.getRequirement().satisfy(player, tool)) {
            return true; // 不满足要求，取消破坏（错误消息已在satisfy方法中发送）
        }

        // 5. 如果都满足，读条 +1，然后取消方块破坏事件
        area.getProgress().increase();

        // 6. 显示进度条 (如 1/5)
        area.getProgressBar().show(player, area.getProgress().getCurrent(), area.getProgress().getMaxBreakTimes());

        // 7. 当区域读条进度满时 (5/5)
        if (area.getProgress().isBreakComplete()) {
            completeAreaBreaking(area, area.getProgress(), player);
        }

        // 总是取消方块破坏事件
        return true;
    }

    /**
     * 完成区域破坏 - 严格按照要求的逻辑
     * 当区域读条进度满时 (5/5)：
     * 1. 清除 blocks 中的所有方块
     * 2. 替换为 refreshing_blocks 中定义的方块
     * 3. 执行 drops (掉落物品、执行命令、给予经验，任意一个均可选)
     * 4. 区域进入刷新阶段
     */
    private void completeAreaBreaking(Area area, AreaProgress progress, Player player) {
        // 1. 清除 blocks 中的所有方块，替换为 refreshing_blocks
        replaceBlocksWithRefreshing(area);

        // 2. 执行 drops (修改掉落物品，执行命令，给予经验，任意一个均可选)
        executeDrops(area, player);

        // 3. 区域进入刷新阶段，此期间方块无法被破坏
        progress.refresh(MMOProfessions.getInstance(), () -> {
            restoreOriginalBlocks(area);
        });
    }

    /**
     * 执行奖励
     */
    private void executeDrops(Area area, Player player) {
        if (area.getRewards() == null) {
            return;
        }

        for (AreaReward reward : area.getRewards()) {
            try {
                reward.give(player);
            } catch (Exception e) {
                MMOProfessions.getInstance().getLogger().warning("执行奖励时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 替换方块为刷新状态 - 严格按照要求的逻辑
     * 清除 blocks 中的所有方块，替换为 refreshing_blocks 中定义的方块
     */
    private void replaceBlocksWithRefreshing(Area area) {
        try {
            // 1. 清除 blocks 中的所有方块
            for (AreaBlock block : area.getBlocks()) {
                block.setToAir();
            }

            // 2. 替换为 refreshing_blocks 中定义的方块
            for (AreaBlock refreshingBlock : area.getRefreshingBlocks()) {
                // 扔到世界里去
                refreshingBlock.putInWorld();
            }
        } catch (Exception e) {
            MMOProfessions.getInstance().getLogger().severe("替换区域 " + area.getId() + " 方块时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 恢复原始方块 - 严格按照要求的逻辑
     * 刷新时间过后，替换 refreshing_blocks 为 blocks，读条清零
     */
    private void restoreOriginalBlocks(Area area) {
        try {
            // 替换 refreshing_blocks 为 blocks
            for (AreaBlock block : area.getBlocks()) {
                block.putInWorld();
            }
        } catch (Exception e) {
            MMOProfessions.getInstance().getLogger().severe("恢复区域 " + area.getId() + " 原始方块时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取区域配置
     */
    public Area getArea(String areaId) {
        return areas.get(areaId);
    }

    /**
     * 获取所有区域
     */
    public Map<String, Area> getAreas() {
        return new HashMap<>(areas);
    }

}
