package cn.fd.customize.mmoProfessions.alchemy;

import org.bukkit.inventory.ItemStack;

import com.google.gson.annotations.Expose;

import java.util.*;

/**
 * 炼金任务数据类
 * 存储炼金任务的所有信息，包括输入物品、输出物品、进度、队列等
 */
public class AlchemyTask {

    private final int taskId; // 队列索引，也是任务ID (不序列化，通过上下文获取)
    @Expose
    private final List<ItemStack> inputItems;
    @Expose
    private final ItemStack stabilizer; // 稳定剂只能有一个 (可空)
    @Expose
    private List<ItemStack> outputItems; // 任务完成后生成
    @Expose
    private boolean outputProcessed = false; // 标记输出是否已被处理
    @Expose
    private final long startTime;
    @Expose
    private final long duration; // 任务持续时间（毫秒）

    /**
     * 创建新的炼金任务
     */
    public AlchemyTask(int taskId, List<ItemStack> inputItems, ItemStack stabilizer, long duration) {
        this(taskId, inputItems, stabilizer, System.currentTimeMillis(), duration);
    }

    public AlchemyTask(int taskId, List<ItemStack> inputItems, ItemStack stabilizer, long startTime, long duration) {
        this.taskId = taskId;
        this.inputItems = new ArrayList<>(inputItems);
        this.stabilizer = stabilizer;
        this.outputItems = null;
        this.startTime = startTime;
        this.duration = duration;
    }

    /**
     * 检查任务是否完成
     */
    public boolean isCompleted() {
        return System.currentTimeMillis() >= startTime + duration;
    }

    /**
     * 获取剩余时间（毫秒）
     */
    public long getRemainingTime() {
        if (isCompleted()) {
            return 0;
        }

        long elapsed = System.currentTimeMillis() - startTime;
        return Math.max(0, duration - elapsed);
    }

    /**
     * 计算任务时间进度（0.0 - 1.0）
     */
    public double calculateProgress() {
        if (isCompleted()) {
            return 1.0;
        }

        long elapsed = System.currentTimeMillis() - startTime;
        return Math.min(1.0, (double) elapsed / duration);
    }

    public int getTaskId() {
        return taskId;
    }

    public List<ItemStack> getInputItems() {
        return new ArrayList<>(inputItems);
    }

    public ItemStack getStabilizer() {
        return stabilizer;
    }

    public List<ItemStack> getOutputItems() {
        return outputItems == null ? new ArrayList<>() : new ArrayList<>(outputItems);
    }

    /**
     * 设置输出物品（任务完成时调用）
     */
    public void setOutputItems(List<ItemStack> items) {
        this.outputItems = new ArrayList<>(items);
    }

    /**
     * 检查输出物品是否已经被处理过
     */
    public boolean isOutputProcessed() {
        return outputProcessed;
    }

    /**
     * 标记输出物品已经被处理
     */
    public void markOutputProcessed() {
        this.outputProcessed = true;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getDuration() {
        return duration;
    }

}
