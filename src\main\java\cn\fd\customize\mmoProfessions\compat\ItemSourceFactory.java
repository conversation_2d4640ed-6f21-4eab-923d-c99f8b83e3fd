package cn.fd.customize.mmoProfessions.compat;

/**
 * 物品源工厂接口
 * 负责创建和管理特定类型的物品源
 */
public interface ItemSourceFactory {

    /**
     * 创建物品源实例
     * 
     * @param value 物品源配置值
     * @return 创建的物品源实例
     */
    ItemSource create(String value);

    /**
     * 检查此物品源类型是否可用
     * 
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 获取物品源类型名称
     * 
     * @return 物品源类型名称
     */
    String getSourceType();

}
