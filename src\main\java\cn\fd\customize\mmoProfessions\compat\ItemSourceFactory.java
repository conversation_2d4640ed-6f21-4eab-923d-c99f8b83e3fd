package cn.fd.customize.mmoProfessions.compat;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import java.util.Random;

/**
 * 物品源工厂接口
 * 负责创建和管理特定类型的物品源
 */
public interface ItemSourceFactory {

    /**
     * 创建物品源实例
     * @param value 物品源配置值
     * @return 创建的物品源实例
     */
    ItemSource create(String value);

    /**
     * 检查此物品源类型是否可用
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 获取物品源类型名称
     * @return 物品源类型名称
     */
    String getSourceType();

    /**
     * 获取固定数量的输出物品
     * 这个方法替代了原来的generateOutput方法，支持amount参数
     *
     * @param value 物品源配置值
     * @param amount 数量字符串，可以是单纯数字或区间（如"1-5"）
     * @param player 玩家
     * @return 生成的物品，已设置正确的数量
     */
    default ItemStack getFixedOutput(String value, String amount, Player player) {
        // 创建物品源实例
        ItemSource source = create(value);

        // 生成基础物品
        ItemStack item = source.generateOutput(player);

        // 解析并设置数量
        int finalAmount = parseAmount(amount);
        item.setAmount(finalAmount);

        return item;
    }

    /**
     * 解析数量字符串
     * 支持单纯数字或区间格式（如"1-5"）
     *
     * @param amount 数量字符串
     * @return 解析后的数量
     */
    public static int parseAmount(String amount) {
        if (amount == null || amount.trim().isEmpty()) {
            return 1; // 默认数量为1
        }

        amount = amount.trim();

        // 检查是否是区间格式
        if (amount.contains("-")) {
            String[] parts = amount.split("-", 2);
            if (parts.length == 2) {
                try {
                    int min = Integer.parseInt(parts[0].trim());
                    int max = Integer.parseInt(parts[1].trim());

                    if (min > max) {
                        // 如果最小值大于最大值，交换它们
                        int temp = min;
                        min = max;
                        max = temp;
                    }

                    // 在区间内随机生成一个数
                    Random random = new Random();
                    return random.nextInt(max - min + 1) + min;
                } catch (NumberFormatException e) {
                    throw new IllegalArgumentException("无效的区间格式: " + amount);
                }
            }
        }

        // 单纯数字
        try {
            return Integer.parseInt(amount);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无效的数量格式: " + amount);
        }
    }
}
