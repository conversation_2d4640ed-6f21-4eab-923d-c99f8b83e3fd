package cn.fd.customize.mmoProfessions.listeners;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * 玩家数据事件监听器
 * 处理玩家上线下线时的数据加载和保存
 */
public class PlayerDataListener implements Listener {

    public PlayerDataListener() {
    }

    /**
     * 玩家上线事件 - 立即加载数据
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        MMOProfessions.getInstance().getServer().getScheduler().runTask(MMOProfessions.getInstance(),
                () -> {
                    try {
                        MMOProfessions.getDataStorage().loadPlayerData(player.getUniqueId());
                        MMOProfessions.getInstance().getLogger().fine("玩家 " + player.getName() + " 上线，已加载数据");
                    } catch (Exception e) {
                        MMOProfessions.getInstance().getLogger()
                                .severe("加载玩家 " + player.getName() + " 数据时发生错误: " + e.getMessage());
                        e.printStackTrace();
                    }
                });
    }

    /**
     * 玩家下线事件 - 立即保存数据
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        MMOProfessions.getInstance().getServer().getScheduler().runTask(MMOProfessions.getInstance(),
                () -> {
                    try {
                        MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());
                        MMOProfessions.getInstance().getLogger().fine("玩家 " + player.getName() + " 下线，已保存数据");
                    } catch (Exception e) {
                        MMOProfessions.getInstance().getLogger()
                                .severe("保存玩家 " + player.getName() + " 数据时发生错误: " + e.getMessage());
                        e.printStackTrace();
                    }
                });
    }

}
