package cn.fd.customize.mmoProfessions.core;

import org.bukkit.entity.Player;

/**
 * 职业接口
 */
public interface Profession {

    /**
     * 获取职业ID
     */
    String getId();

    /**
     * 获取职业显示名称
     */
    String getDisplayName();

    /**
     * 获取职业描述
     */
    String getDescription();

    /**
     * 玩家获得经验时调用
     */
    void onExpGain(Player player, int amount);

    /**
     * 玩家升级时调用
     */
    void onLevelUp(Player player, int newLevel);

    /**
     * 检查玩家是否可以使用此职业
     */
    boolean canUse(Player player);

}
