package cn.fd.customize.mmoProfessions.compat;

/**
 * 物品源抽象基类
 * 提供通用的解析和处理逻辑
 */
public abstract class AbstractItemSource implements ItemSource {

    protected final ItemSourceFactory factory;
    protected final String value;

    public AbstractItemSource(ItemSourceFactory factory, String value) {
        this.factory = factory;
        this.value = value;
    }

    @Override
    public ItemSourceFactory getFactory() {
        return factory;
    }

    @Override
    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return String.format("%s{source=%s, value=%s}",
                getClass().getSimpleName(), factory.getSourceType(), value);
    }

}
