package cn.fd.customize.mmoProfessions.compat;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 物品源解析器
 * 负责解析配置文件中的物品源配置，并创建对应的ItemSource对象
 */
public class ItemParser {
    /**
     * 从Map解析单个物品源
     *
     * @param configMap 配置Map，包含source和value字段
     * @return 解析出的ItemSource对象
     * @throws IllegalArgumentException 如果配置格式不正确
     */
    @SuppressWarnings("unchecked")
    public static ItemSource parseItemSource(Object configMap) {
        if (configMap == null) {
            throw new IllegalArgumentException("物品源配置不能为空");
        } else if (!(configMap instanceof Map)) {
            throw new IllegalArgumentException("物品源配置必须是Map类型");
        }

        try {

            Map<String, Object> map = (Map<String, Object>) configMap;

            Object source = map.get("source");
            Object value = map.get("value");

            if (source == null || value == null) {
                throw new IllegalArgumentException("物品源配置必须包含 'source' 和 'value' 字段");
            } else {
                return createItemSource(source.toString(), value.toString());
            }
        } catch (ClassCastException e) {
            throw new IllegalArgumentException("错误的物品源配置: " + configMap, e);
        }
    }

    /**
     * 从Map解析带数量的物品源
     *
     * @param configMap 配置Map，包含source、value和amount字段
     * @return 解析出的ItemSourceWithAmount对象
     * @throws IllegalArgumentException 如果配置格式不正确
     */
    @SuppressWarnings("unchecked")
    public static ItemSourceWithAmount parseItemSourceWithAmount(Object configMap) {
        if (configMap == null) {
            throw new IllegalArgumentException("物品源配置不能为空");
        } else if (!(configMap instanceof Map)) {
            throw new IllegalArgumentException("物品源配置必须是Map类型");
        }

        try {
            Map<String, Object> map = (Map<String, Object>) configMap;

            Object source = map.get("source");
            Object value = map.get("value");
            Object amount = map.get("amount");

            if (source == null || value == null) {
                throw new IllegalArgumentException("物品源配置必须包含 'source' 和 'value' 字段");
            }

            ItemSource itemSource = createItemSource(source.toString(), value.toString());
            String amountStr = amount != null ? amount.toString() : "1";

            return new ItemSourceWithAmount(itemSource, amountStr);
        } catch (ClassCastException e) {
            throw new IllegalArgumentException("错误的物品源配置: " + configMap, e);
        }
    }

    /**
     * 创建物品源实例
     * 
     * @param source 物品源类型
     * @param value  物品源值
     * @return 创建的物品源对象
     * @throws IllegalArgumentException 如果source类型不支持
     */
    private static ItemSource createItemSource(String source, String value) {
        if (source == null || value == null) {
            throw new IllegalArgumentException("物品源类型和值不能为空");
        }

        source = source.toLowerCase().trim();
        value = value.trim();

        // 使用注册机制验证和创建物品源
        if (!ItemSourceRegistry.isSourceAvailable(source)) {
            throw new IllegalArgumentException("不支持的物品源类型或插件未安装: " + source);
        }

        return ItemSourceRegistry.createItemSource(source, value);
    }

    /**
     * 从配置列表解析多个物品源
     * 
     * @param configList 配置列表，每个元素都是包含source和value的配置
     * @return 解析出的ItemSource列表
     */
    @SuppressWarnings("unchecked")
    public static List<ItemSource> parseItemSources(List<?> configList) {
        List<ItemSource> sources = new ArrayList<>();

        if (configList == null || configList.isEmpty()) {
            return sources;
        }

        for (Object configObj : configList) {
            try {
                ItemSource source;
                if (configObj instanceof Map) {
                    source = parseItemSource((Map<String, Object>) configObj);
                } else {
                    MMOProfessions.getInstance().getLogger().warning(
                            "不支持的物品源配置格式: " + configObj.getClass().getSimpleName());
                    continue;
                }

                sources.add(source);
            } catch (Exception e) {
                MMOProfessions.getInstance().getLogger().warning(
                        "解析物品源失败: " + configObj + " - " + e.getMessage());
            }
        }

        return sources;
    }

    /**
     * 获取支持的物品源类型列表
     * 
     * @return 支持的物品源类型
     */
    public static List<String> getSupportedSources() {
        return new ArrayList<>(ItemSourceRegistry.getRegisteredSources());
    }
}
