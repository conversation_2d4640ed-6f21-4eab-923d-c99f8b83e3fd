package cn.fd.customize.mmoProfessions.area;

import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

/**
 * 进度条显示配置类
 *
 * 支持多种显示方式：
 * - TITLE: 在屏幕中央显示大标题
 * - SUBTITLE: 在屏幕中央显示副标题（推荐使用）
 * - ACTIONBAR: 在物品栏上方显示动作栏文本
 *
 * 支持的占位符：
 * - {current} 或 {0}: 当前进度数值
 * - {max} 或 {1}: 最大进度数值
 * - {percentage}: 百分比进度
 * - {bar}: 默认可视化进度条
 * - {bar:长度:满字符:空字符}: 自定义进度条
 * 例如: {bar:10:█:░} 创建长度为10的进度条
 *
 * 使用示例：
 * new ProgressBar(DisplayType.SUBTITLE, "&6进度: &e{current}/{max}
 * &7{bar:15:■:□}")
 */
public class ProgressBar {

    private final DisplayType type;
    private final String template;

    public ProgressBar(DisplayType type, String template) {
        this.type = type;
        this.template = template;
    }

    public DisplayType getType() {
        return type;
    }

    public String getTemplate() {
        return template;
    }

    /**
     * 显示进度条给玩家
     * 
     * @param player  玩家
     * @param current 当前进度
     * @param max     最大进度
     */
    public void show(Player player, int current, int max) {
        if (player == null || !player.isOnline()) {
            return;
        }

        String formattedText = formatText(current, max);

        switch (type) {
            case TITLE:
                player.sendTitle(formattedText, "", 0, 20, 10);
                break;

            case SUBTITLE:
                player.sendTitle("", formattedText, 0, 20, 10);
                break;

            case ACTIONBAR:
                sendActionBar(player, formattedText);
                break;
        }
    }

    /**
     * 格式化显示文本
     * 
     * @param current 当前进度
     * @param max     最大进度
     * @return 格式化后的文本
     */
    public String formatText(int current, int max) {
        String result = template
                .replace("{current}", String.valueOf(current))
                .replace("{max}", String.valueOf(max))
                .replace("{0}", String.valueOf(current))
                .replace("{1}", String.valueOf(max))
                .replace("{percentage}", String.format("%.1f", (double) current / max * 100));

        // 处理自定义bar占位符
        result = processCustomBarPlaceholders(result, current, max);

        // 处理默认bar占位符
        result = result.replace("{bar}", createDefaultVisualBar(current, max));

        return ChatColor.translateAlternateColorCodes('&', result);
    }

    /**
     * 处理自定义bar占位符
     * 支持格式：{bar:length:fullChar:emptyChar}
     * 例如：{bar:10:█:░} 创建长度为10，满字符为█，空字符为░的进度条
     *
     * @param text    包含占位符的文本
     * @param current 当前进度
     * @param max     最大进度
     * @return 处理后的文本
     */
    private String processCustomBarPlaceholders(String text, int current, int max) {
        // 使用正则表达式匹配 {bar:length:fullChar:emptyChar} 格式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\{bar:(\\d+):(.):(.)}");
        java.util.regex.Matcher matcher = pattern.matcher(text);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            int length = Integer.parseInt(matcher.group(1));
            char fullChar = matcher.group(2).charAt(0);
            char emptyChar = matcher.group(3).charAt(0);

            String customBar = createCustomVisualBar(current, max, length, fullChar, emptyChar);
            matcher.appendReplacement(result, java.util.regex.Matcher.quoteReplacement(customBar));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 创建自定义可视化进度条
     * 
     * @param current   当前进度
     * @param max       最大进度
     * @param length    进度条长度
     * @param fullChar  满字符
     * @param emptyChar 空字符
     * @return 可视化进度条字符串
     */
    private String createCustomVisualBar(int current, int max, int length, char fullChar, char emptyChar) {
        if (max <= 0 || length <= 0) {
            return "";
        }

        double percentage = (double) current / max;
        int completedLength = (int) (percentage * length);
        int remainingLength = length - completedLength;

        StringBuilder bar = new StringBuilder();

        // 添加已完成部分
        for (int i = 0; i < completedLength; i++) {
            bar.append(fullChar);
        }

        // 添加剩余部分
        for (int i = 0; i < remainingLength; i++) {
            bar.append(emptyChar);
        }

        return bar.toString();
    }

    /**
     * 创建默认可视化进度条
     * 
     * @param current 当前进度
     * @param max     最大进度
     * @return 可视化进度条字符串
     */
    private String createDefaultVisualBar(int current, int max) {
        return createCustomVisualBar(current, max, 5, '█', '░');
    }

    /**
     * 发送ActionBar消息
     * 
     * @param player  玩家
     * @param message 消息内容
     */
    private void sendActionBar(Player player, String message) {
        try {
            // 使用Spigot API发送ActionBar
            player.spigot().sendMessage(
                    net.md_5.bungee.api.ChatMessageType.ACTION_BAR,
                    net.md_5.bungee.api.chat.TextComponent.fromLegacyText(message));
        } catch (Exception e) {
            // 如果ActionBar发送失败，回退到聊天消息
            player.sendMessage(message);
        }
    }

    /**
     * 清除玩家的标题显示
     * 
     * @param player 玩家
     */
    public static void clearTitle(Player player) {
        if (player != null && player.isOnline()) {
            player.sendTitle("", "", 0, 0, 0);
        }
    }

    /**
     * 进度条显示类型枚举
     */
    public enum DisplayType {
        TITLE, // 标题
        SUBTITLE, // 副标题
        ACTIONBAR // 动作栏
    }

}
