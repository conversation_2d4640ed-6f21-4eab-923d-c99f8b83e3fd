package cn.fd.customize.mmoProfessions.professions;

import org.bukkit.entity.Player;

/**
 * 锻造职业
 */
public class ForgingProfession extends AbstractProfession {
    
    public ForgingProfession() {
        super("forging", "锻造", "通过锻造装备和工具获得经验");
    }
    
    @Override
    public void onExpGain(Player player, int amount) {
        super.onExpGain(player, amount);
        // 可以添加锻造特有的经验获得效果
    }
    
    @Override
    public void onLevelUp(Player player, int newLevel) {
        super.onLevelUp(player, newLevel);
        // 特殊升级消息现在通过MessageManager在messages.yml中配置
    }
    
    /**
     * 检查玩家是否可以锻造指定等级的物品
     */
    public boolean canForge(Player player, int requiredLevel) {
        return hasLevel(player, requiredLevel);
    }
    
    /**
     * 获取最大同时锻造数量（基于等级）
     */
    public int getMaxConcurrentForging(Player player) {
        int level = getLevel(player);
        if (level < 20) return 1;
        if (level < 35) return 2;
        if (level < 45) return 3;
        return 4; // 最高4个
    }
    
    /**
     * 获取锻造时间减少比例（基于等级）
     */
    public double getTimeReduction(Player player) {
        int level = getLevel(player);
        if (level < 30) return 0.0;
        return Math.min(0.4, (level - 30) * 0.015); // 30级后每级减少1.5%时间，最高减少40%
    }
    
    /**
     * 获取额外产物几率（基于等级）
     */
    public double getExtraOutputChance(Player player) {
        int level = getLevel(player);
        if (level < 40) return 0.0;
        return Math.min(0.2, (level - 40) * 0.015); // 40级后每级增加1.5%，最高20%
    }
    
    /**
     * 获取锻造成功率加成（基于等级）
     */
    public double getSuccessRateBonus(Player player) {
        int level = getLevel(player);
        return Math.min(0.25, level * 0.004); // 每级增加0.4%成功率，最高25%
    }
    
    /**
     * 获取材料节省几率（基于等级）
     */
    public double getMaterialSaveChance(Player player) {
        int level = getLevel(player);
        if (level < 25) return 0.0;
        return Math.min(0.15, (level - 25) * 0.005); // 25级后每级增加0.5%，最高15%
    }
}
