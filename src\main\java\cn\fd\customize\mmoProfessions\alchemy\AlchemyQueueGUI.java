// package cn.fd.customize.mmoProfessions.alchemy;

// import cn.fd.customize.mmoProfessions.MMOProfessions;
// import cn.fd.customize.mmoProfessions.config.AlchemyConfigLoader;
// import org.bukkit.Bukkit;
// import org.bukkit.Material;
// import org.bukkit.configuration.ConfigurationSection;
// import org.bukkit.entity.Player;
// import org.bukkit.event.EventHandler;
// import org.bukkit.event.Listener;
// import org.bukkit.event.inventory.ClickType;
// import org.bukkit.event.inventory.InventoryClickEvent;
// import org.bukkit.event.inventory.InventoryCloseEvent;
// import org.bukkit.inventory.Inventory;
// import org.bukkit.inventory.ItemStack;
// import org.bukkit.inventory.meta.ItemMeta;

// import java.util.*;

// /**
//  * 炼金队列查看界面
//  * 显示队列中的任务信息、进度条、取消任务功能
//  */
// public class AlchemyQueueGUI {

//     private final AlchemyManager alchemyManager;
//     private final Map<UUID, QueueInventoryData> playerInventories;

//     // 配置项
//     private String guiTitle;
//     private List<Integer> taskSlots;
//     private int backButtonSlot;
//     private int refreshButtonSlot;
//     private ItemStack taskButton;
//     private ItemStack progressButton;
//     private ItemStack backButton;
//     private ItemStack refreshButton;
//     private ItemStack fillerItem;

//     public AlchemyQueueGUI(AlchemyManager alchemyManager) {
//         this.alchemyManager = alchemyManager;
//         this.playerInventories = new HashMap<>();

//         loadConfiguration();
//     }

//     /**
//      * 队列界面数据
//      */
//     private static class QueueInventoryData {
//         final Inventory inventory;
//         final int queueIndex;

//         QueueInventoryData(Inventory inventory, int queueIndex) {
//             this.inventory = inventory;
//             this.queueIndex = queueIndex;
//         }
//     }

//     /**
//      * 加载配置
//      */
//     private void loadConfiguration() {
//         guiTitle = alchemyManager.getConfigManager().getQueueGUITitle();
//         taskSlots = alchemyManager.getConfigManager().getTaskSlots();
//         backButtonSlot = alchemyManager.getConfigManager().getBackButtonSlot();
//         refreshButtonSlot = alchemyManager.getConfigManager().getRefreshButtonSlot();

//         // 加载按钮物品
//         taskButton = alchemyManager.getConfigManager().createItemFromConfig("queue_gui.buttons.task");
//         progressButton = alchemyManager.getConfigManager().createItemFromConfig("queue_gui.buttons.progress");
//         backButton = alchemyManager.getConfigManager().createItemFromConfig("queue_gui.buttons.back");
//         refreshButton = alchemyManager.getConfigManager().createItemFromConfig("queue_gui.buttons.refresh");
//         fillerItem = alchemyManager.getConfigManager().createItemFromConfig("queue_gui.buttons.filler");
//     }

//     /**
//      * 使用默认配置
//      */
//     private void useDefaultConfiguration() {
//         guiTitle = "§6炼金队列 - {queue_index}";
//         taskSlots = Arrays.asList(10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34);
//         backButtonSlot = 45;
//         refreshButtonSlot = 53;

//         // 默认按钮
//         taskButton = createDefaultTaskButton();
//         progressButton = createDefaultProgressButton();
//         backButton = createDefaultBackButton();
//         refreshButton = createDefaultRefreshButton();
//         fillerItem = createDefaultFillerItem();
//     }

//     /**
//      * 从配置加载物品
//      */
//     private ItemStack loadItemFromConfig(ConfigurationSection section) {
//         if (section == null) {
//             return new ItemStack(Material.BARRIER);
//         }

//         Object itemObj = section.get("item");
//         if (itemObj instanceof ItemStack) {
//             return (ItemStack) itemObj;
//         }

//         Material material = Material.valueOf(section.getString("material", "BARRIER"));
//         ItemStack item = new ItemStack(material);
//         ItemMeta meta = item.getItemMeta();
//         if (meta != null) {
//             meta.setDisplayName(section.getString("name", ""));
//             meta.setLore(section.getStringList("lore"));
//             item.setItemMeta(meta);
//         }

//         return item;
//     }

//     /**
//      * 创建默认任务按钮
//      */
//     private ItemStack createDefaultTaskButton() {
//         ItemStack item = new ItemStack(Material.BREWING_STAND);
//         ItemMeta meta = item.getItemMeta();
//         if (meta != null) {
//             meta.setDisplayName("§e炼金任务 #{task_number}");
//             meta.setLore(Arrays.asList(
//                     "§7任务ID: §f{task_id}",
//                     "§7进度: §a{progress}%",
//                     "§7剩余时间: §e{remaining_time}",
//                     "§7状态: §b{status}",
//                     "",
//                     "§e左键: §7查看投入物品",
//                     "§c右键: §7取消任务"));
//             item.setItemMeta(meta);
//         }
//         return item;
//     }

//     /**
//      * 创建默认进度按钮
//      */
//     private ItemStack createDefaultProgressButton() {
//         ItemStack item = new ItemStack(Material.CLOCK);
//         ItemMeta meta = item.getItemMeta();
//         if (meta != null) {
//             meta.setDisplayName("§a进度: {progress}%");
//             meta.setLore(Arrays.asList(
//                     "§7{progress_bar}",
//                     "§7剩余时间: §e{remaining_time}"));
//             item.setItemMeta(meta);
//         }
//         return item;
//     }

//     /**
//      * 创建默认返回按钮
//      */
//     private ItemStack createDefaultBackButton() {
//         ItemStack item = new ItemStack(Material.ARROW);
//         ItemMeta meta = item.getItemMeta();
//         if (meta != null) {
//             meta.setDisplayName("§7返回炼金台");
//             meta.setLore(Arrays.asList("§7点击返回主界面"));
//             item.setItemMeta(meta);
//         }
//         return item;
//     }

//     /**
//      * 创建默认刷新按钮
//      */
//     private ItemStack createDefaultRefreshButton() {
//         ItemStack item = new ItemStack(Material.EMERALD);
//         ItemMeta meta = item.getItemMeta();
//         if (meta != null) {
//             meta.setDisplayName("§a刷新");
//             meta.setLore(Arrays.asList("§7点击刷新队列信息"));
//             item.setItemMeta(meta);
//         }
//         return item;
//     }

//     /**
//      * 创建默认填充物品
//      */
//     private ItemStack createDefaultFillerItem() {
//         ItemStack item = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
//         ItemMeta meta = item.getItemMeta();
//         if (meta != null) {
//             meta.setDisplayName(" ");
//             item.setItemMeta(meta);
//         }
//         return item;
//     }

//     /**
//      * 打开队列界面
//      */
//     public void openGUI(Player player, int queueIndex) {
//         String title = guiTitle.replace("{queue_index}", String.valueOf(queueIndex));
//         Inventory inventory = Bukkit.createInventory(null, 27, title);

//         // 设置填充物品
//         for (int i = 0; i < 27; i++) {
//             inventory.setItem(i, fillerItem.clone());
//         }

//         // 设置返回按钮
//         inventory.setItem(backButtonSlot, backButton.clone());

//         // 设置刷新按钮
//         inventory.setItem(refreshButtonSlot, refreshButton.clone());

//         // 更新任务显示
//         updateTaskDisplay(inventory, player, queueIndex);

//         QueueInventoryData data = new QueueInventoryData(inventory, queueIndex);
//         playerInventories.put(player.getUniqueId(), data);
//         player.openInventory(inventory);
//     }

//     /**
//      * 更新任务显示
//      */
//     private void updateTaskDisplay(Inventory inventory, Player player, int queueIndex) {
//         List<AlchemyTask> tasks = alchemyManager.getTaskManager().getPlayerQueueTasks(player.getUniqueId(), queueIndex);

//         // 清空任务槽位
//         for (int slot : taskSlots) {
//             inventory.setItem(slot, fillerItem.clone());
//         }

//         // 显示任务
//         for (int i = 0; i < Math.min(tasks.size(), taskSlots.size()); i++) {
//             AlchemyTask task = tasks.get(i);
//             int slot = taskSlots.get(i);

//             ItemStack taskItem = createTaskItem(task, i + 1);
//             inventory.setItem(slot, taskItem);
//         }
//     }

//     /**
//      * 创建任务物品
//      */
//     private ItemStack createTaskItem(AlchemyTask task, int taskNumber) {
//         ItemStack item = taskButton.clone();
//         ItemMeta meta = item.getItemMeta();
//         if (meta != null) {
//             // 替换占位符
//             String displayName = meta.getDisplayName()
//                     .replace("{task_number}", String.valueOf(taskNumber))
//                     .replace("{task_id}", task.getTaskId().toString().substring(0, 8))
//                     .replace("{progress}", String.format("%.1f", task.getProgress() * 100))
//                     .replace("{remaining_time}", task.getFormattedRemainingTime())
//                     .replace("{status}", getTaskStatus(task));
//             meta.setDisplayName(displayName);

//             List<String> lore = new ArrayList<>();
//             for (String line : meta.getLore()) {
//                 lore.add(line
//                         .replace("{task_number}", String.valueOf(taskNumber))
//                         .replace("{task_id}", task.getTaskId().toString().substring(0, 8))
//                         .replace("{progress}", String.format("%.1f", task.getProgress() * 100))
//                         .replace("{remaining_time}", task.getFormattedRemainingTime())
//                         .replace("{status}", getTaskStatus(task)));
//             }
//             meta.setLore(lore);

//             item.setItemMeta(meta);
//         }

//         return item;
//     }

//     /**
//      * 获取任务状态
//      */
//     private String getTaskStatus(AlchemyTask task) {
//         return task.getStatus().getDisplayName();
//     }

//     /**
//      * 检查是否是玩家的队列界面
//      */
//     public boolean isAlchemyQueueGUI(Player player, Inventory inventory) {
//         QueueInventoryData data = playerInventories.get(player.getUniqueId());
//         return data != null && data.inventory.equals(inventory);
//     }

//     /**
//      * 处理界面点击事件
//      */
//     public void handleInventoryClick(InventoryClickEvent event) {
//         if (!(event.getWhoClicked() instanceof Player)) {
//             return;
//         }

//         Player player = (Player) event.getWhoClicked();
//         QueueInventoryData data = playerInventories.get(player.getUniqueId());

//         if (data == null || !event.getInventory().equals(data.inventory)) {
//             return;
//         }

//         event.setCancelled(true);

//         int slot = event.getRawSlot();

//         // 处理返回按钮
//         if (slot == backButtonSlot) {
//             player.closeInventory();
//             // 发送消息提示玩家使用命令重新打开主界面
//             player.sendMessage("§e使用 /alchemy 命令重新打开炼金台");
//             return;
//         }

//         // 处理刷新按钮
//         if (slot == refreshButtonSlot) {
//             updateTaskDisplay(data.inventory, player, data.queueIndex);
//             return;
//         }

//         // 处理任务点击
//         if (taskSlots.contains(slot)) {
//             int taskIndex = taskSlots.indexOf(slot);
//             List<AlchemyTask> tasks = alchemyManager.getTaskManager().getPlayerQueueTasks(player.getUniqueId(),
//                     data.queueIndex);

//             if (taskIndex < tasks.size()) {
//                 AlchemyTask task = tasks.get(taskIndex);
//                 handleTaskClick(player, task, event.getClick());
//             }
//         }
//     }

//     /**
//      * 处理任务点击
//      */
//     private void handleTaskClick(Player player, AlchemyTask task, ClickType clickType) {
//         if (clickType == ClickType.LEFT) {
//             // 左键：查看投入物品
//             showTaskInputs(player, task);
//         } else if (clickType == ClickType.RIGHT) {
//             // 右键：取消任务（需要确认）
//             showCancelConfirmation(player, task);
//         }
//     }

//     /**
//      * 显示任务投入物品
//      */
//     private void showTaskInputs(Player player, AlchemyTask task) {
//         // 创建临时界面显示投入物品
//         Inventory inputInventory = Bukkit.createInventory(null, 27, "§6任务投入物品");

//         // 显示输入物品
//         List<ItemStack> inputs = task.getInputItems();
//         for (int i = 0; i < Math.min(inputs.size(), 18); i++) {
//             inputInventory.setItem(i, inputs.get(i));
//         }

//         // 显示催化剂
//         List<ItemStack> catalysts = task.getCatalysts();
//         for (int i = 0; i < Math.min(catalysts.size(), 9); i++) {
//             inputInventory.setItem(18 + i, catalysts.get(i));
//         }

//         player.openInventory(inputInventory);
//     }

//     /**
//      * 显示取消确认
//      */
//     private void showCancelConfirmation(Player player, AlchemyTask task) {
//         if (task.isCompleted() || task.isCancelled()) {
//             player.sendMessage("§c该任务已完成或已取消，无法再次取消！");
//             return;
//         }

//         // 创建确认界面
//         Inventory confirmInventory = Bukkit.createInventory(null, 27, "§c确认取消任务");

//         // 确认按钮
//         ItemStack confirmButton = new ItemStack(Material.RED_CONCRETE);
//         ItemMeta confirmMeta = confirmButton.getItemMeta();
//         if (confirmMeta != null) {
//             confirmMeta.setDisplayName("§c确认取消");
//             confirmMeta.setLore(Arrays.asList(
//                     "§7点击确认取消任务",
//                     "§c警告: 取消后不会返还投入的材料！"));
//             confirmButton.setItemMeta(confirmMeta);
//         }

//         // 取消按钮
//         ItemStack cancelButton = new ItemStack(Material.GREEN_CONCRETE);
//         ItemMeta cancelMeta = cancelButton.getItemMeta();
//         if (cancelMeta != null) {
//             cancelMeta.setDisplayName("§a返回");
//             cancelMeta.setLore(Arrays.asList("§7点击返回队列界面"));
//             cancelButton.setItemMeta(cancelMeta);
//         }

//         confirmInventory.setItem(11, confirmButton);
//         confirmInventory.setItem(15, cancelButton);

//         // 存储任务ID用于确认
//         // 这里可以使用更复杂的方式来存储任务引用
//         player.openInventory(confirmInventory);

//         // 简化处理：直接发送确认消息
//         player.closeInventory();
//         player.sendMessage("§e请在聊天中输入 'confirm' 来确认取消任务，或输入 'cancel' 来取消操作");

//         // 实际实现中，这里应该使用更好的确认机制
//         // 比如使用AnvilGUI或者聊天监听器
//     }

//     /**
//      * 处理界面关闭事件
//      */
//     public void handleInventoryClose(InventoryCloseEvent event) {
//         if (!(event.getPlayer() instanceof Player)) {
//             return;
//         }

//         Player player = (Player) event.getPlayer();
//         playerInventories.remove(player.getUniqueId());
//     }
// }
