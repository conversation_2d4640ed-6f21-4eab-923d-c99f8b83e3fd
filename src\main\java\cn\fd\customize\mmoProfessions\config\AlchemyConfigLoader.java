package cn.fd.customize.mmoProfessions.config;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.alchemy.AlchemyRecipe;
import cn.fd.customize.mmoProfessions.alchemy.LevelChoice;
import cn.fd.customize.mmoProfessions.alchemy.Stabilizer;
import cn.fd.customize.mmoProfessions.compat.ItemParser;
import cn.fd.customize.mmoProfessions.compat.ItemSource;
import cn.fd.customize.mmoProfessions.compat.ItemSourceWithAmount;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 炼金配置管理器
 * 管理alchemy.yml配置文件的加载和访问
 */
public class AlchemyConfigLoader {

    private final File configFile;
    private FileConfiguration config;

    public AlchemyConfigLoader() {
        this.configFile = new File(MMOProfessions.getInstance().getDataFolder(), "alchemy.yml");
        reloadConfig();
    }

    /**
     * 加载配置文件
     */
    public void reloadConfig() {
        // 如果配置文件不存在，从资源中复制
        if (!configFile.exists()) {
            saveDefaultConfig();
        }

        config = YamlConfiguration.loadConfiguration(configFile);
    }

    /**
     * 保存默认配置文件
     */
    private void saveDefaultConfig() {
        try {
            // 确保父目录存在
            configFile.getParentFile().mkdirs();

            // 从资源中复制配置文件
            InputStream inputStream = MMOProfessions.getInstance().getResource("alchemy.yml");
            if (inputStream != null) {
                Files.copy(inputStream, configFile.toPath());
                inputStream.close();
            } else {
                MMOProfessions.getInstance().getLogger().warning("无法找到默认炼金配置文件资源");
            }
        } catch (IOException e) {
            MMOProfessions.getInstance().getLogger().severe("创建默认炼金配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 保存配置
     */
    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            MMOProfessions.getInstance().getLogger().severe("保存炼金配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置对象
     */
    public FileConfiguration getConfig() {
        return config;
    }

    public int getBaseQueueCount() {
        return config.getInt("gui.base_queue_count", 0);
    }

    public int getMaxQueueCount() {
        return getQueueButtonSlots().size();
    }

    public List<Integer> getQueueButtonSlots() {
        return config.getIntegerList("gui.queue_button_slots");
    }

    /**
     * 获取任务槽位置
     */
    public List<Integer> getTaskSlots() {
        return config.getIntegerList("queue_gui.task_slots");
    }

    /**
     * 获取返回按钮槽位置
     */
    public int getBackButtonSlot() {
        return config.getInt("queue_gui.back_button_slot");
    }

    /**
     * 获取刷新按钮槽位置
     */
    public int getRefreshButtonSlot() {
        return config.getInt("queue_gui.refresh_button_slot");
    }

    public String formatRemainingTime(long remainingTime) {
        return config.getString("queue_gui.format_remaining_time", "{minutes}min {seconds}s")
                .replace("{seconds}", String.valueOf(remainingTime / 1000))
                .replace("{minutes}", String.valueOf(remainingTime / 60000))
                .replace("{hours}", String.valueOf(remainingTime / 3600000));
    }

    /**
     * 从配置创建物品（使用ItemStack序列化格式）
     */
    public ItemStack createItemFromConfig(String path) {
        try {
            // 直接使用ItemStack反序列化
            ItemStack itemStack = config.getItemStack(path);
            if (itemStack != null) {
                return itemStack;
            }
        } catch (Exception e) {
            MMOProfessions.getInstance().getLogger().warning("无法从路径反序列化物品: " + path + " - " + e.getMessage());
        }
        // 如果反序列化失败，返回默认物品
        MMOProfessions.getInstance().getLogger().warning("使用默认物品替代: " + path);
        return new ItemStack(Material.BARRIER);
    }

    /**
     * 加载炼金配方
     */
    public List<AlchemyRecipe> loadRecipes() {
        List<AlchemyRecipe> recipes = new ArrayList<>();

        // 获取配方配置节
        List<Map<?, ?>> recipeConfigs = config.getMapList("recipes");
        if (recipeConfigs.isEmpty()) {
            MMOProfessions.getInstance().getLogger().warning("未找到炼金配方配置");
            return recipes;
        }

        int recipeId = 0;
        for (Map<?, ?> recipeConfig : recipeConfigs) {
            try {
                // 解析输入物品
                List<ItemSource> inputs = new ArrayList<>();
                List<?> inputStrings = (List<?>) Objects.requireNonNull( recipeConfig.get("inputs"),"inputs is null");
                for (Object inputString : inputStrings) {
                    inputs.add(ItemParser.parseItemSource(inputString));
                }

                // 解析等级选择
                List<LevelChoice> levelChoices = new ArrayList<>();
                LevelChoice defaultChoice = null;

                @SuppressWarnings("unchecked")
                Map<String, Map<?, ?>> levelChoicesConfig = Objects.requireNonNull(
                        (Map<String, Map<?, ?>>) recipeConfig.get("level_choices"),
                        "level_choices is null");

                // 首先解析 default 配置
                Map<?, ?> defaultConfig = levelChoicesConfig.get("default");
                if (defaultConfig != null) {
                    try {
                        defaultChoice = parseLevelChoice("default", defaultConfig, null);
                    } catch (Exception e) {
                        MMOProfessions.getInstance().getLogger().warning(
                                "无法解析默认等级选择: " + e.getMessage());
                    }
                }

                // 然后解析其他等级选择，使用 default 作为回退
                for (Map.Entry<?, Map<?, ?>> entry : levelChoicesConfig.entrySet()) {
                    String levelKey = entry.getKey().toString();
                    Map<?, ?> choiceConfig = entry.getValue();

                    if ("default".equalsIgnoreCase(levelKey)) {
                        continue; // 已经处理过了
                    }

                    try {
                        LevelChoice choice = parseLevelChoice(levelKey, choiceConfig, defaultConfig);
                        levelChoices.add(choice);
                    } catch (Exception e) {
                        MMOProfessions.getInstance().getLogger().warning(
                                "无法解析等级选择 " + levelKey + ": " + e.getMessage());
                    }
                }

                // 创建配方
                if (!inputs.isEmpty() && (!levelChoices.isEmpty() || defaultChoice != null)) {
                    AlchemyRecipe recipe = new AlchemyRecipe(recipeId++, inputs, levelChoices, defaultChoice);
                    recipes.add(recipe);
                } else {
                    MMOProfessions.getInstance().getLogger().warning("配方配置不完整，跳过");
                }

            } catch (Exception e) {
                MMOProfessions.getInstance().getLogger().warning("加载配方失败: " + e.getMessage());
            }
        }

        MMOProfessions.getInstance().getLogger().info("共加载 " + recipes.size() + " 个炼金配方");
        return recipes;

    }

    /**
     * 解析等级选择配置
     * 
     * @param levelKey      等级键
     * @param choiceConfig  当前等级选择配置
     * @param defaultConfig 默认等级选择配置，用于继承缺失的值
     */
    private LevelChoice parseLevelChoice(String levelKey, Map<?, ?> choiceConfig, Map<?, ?> defaultConfig) {
        int requiredLevel = "default".equalsIgnoreCase(levelKey) ? -1 : Integer.parseInt(levelKey);

        // 解析输出物品（带数量）
        List<ItemSourceWithAmount> outputs = new ArrayList<>();
        List<?> outputsConfig = Objects.requireNonNull(
                (List<?>) choiceConfig.get("outputs"),
                "outputs is null");

        for (Object outputString : outputsConfig) {
            try {
                outputs.add(ItemParser.parseItemSourceWithAmount(outputString));
            } catch (Exception e) {
                MMOProfessions.getInstance().getLogger().warning(
                        "无法解析输出物品: " + outputString + " - " + e.getMessage());
            }
        }

        // 解析持续时间 - 优先使用当前配置，然后是 default 配置，最后是插件默认值
        long duration = 30; // 插件默认值：30秒
        if (choiceConfig.containsKey("duration")) {
            duration = ((Number) choiceConfig.get("duration")).longValue();
        } else if (defaultConfig != null && defaultConfig.containsKey("duration")) {
            duration = ((Number) defaultConfig.get("duration")).longValue();
        }

        // 解析基础成功率 - 优先使用当前配置，然后是 default 配置，最后是插件默认值
        double baseSuccessRate = 0.5; // 插件默认值：50%
        if (choiceConfig.containsKey("base_success_rate")) {
            baseSuccessRate = ((Number) choiceConfig.get("base_success_rate")).doubleValue();
        } else if (defaultConfig != null && defaultConfig.containsKey("base_success_rate")) {
            baseSuccessRate = ((Number) defaultConfig.get("base_success_rate")).doubleValue();
        }

        return new LevelChoice(requiredLevel, outputs, duration, baseSuccessRate);
    }

    /**
     * 加载稳定剂
     */
    public List<Stabilizer> loadStabilizers() {
        List<Stabilizer> stabilizers = new ArrayList<>();

        // 获取稳定剂配置节
        List<Map<?, ?>> stabilizerConfigs = config.getMapList("stabilizers");
        if (stabilizerConfigs.isEmpty()) {
            MMOProfessions.getInstance().getLogger().warning("未找到稳定剂配置");
            return stabilizers;
        }

        for (Map<?, ?> stabilizerConfig : stabilizerConfigs) {
            try {
                // 解析稳定剂物品
                @SuppressWarnings("unchecked")
                Map<String, Object> ingredientValue = (Map<String, Object>) stabilizerConfig.get("ingredient");
                if (ingredientValue == null) {
                    MMOProfessions.getInstance().getLogger().warning("稳定剂配置缺少ingredient字段");
                    continue;
                }

                // 解析成功率加成
                double successRateBonus = 0.0;
                if (stabilizerConfig.containsKey("bonus")) {
                    successRateBonus = ((Number) stabilizerConfig.get("bonus")).doubleValue();
                }

                // 创建稳定剂
                Stabilizer stabilizer = new Stabilizer(ingredientValue, successRateBonus);
                stabilizers.add(stabilizer);

            } catch (Exception e) {
                MMOProfessions.getInstance().getLogger().warning("加载稳定剂失败: " + e.getMessage());
            }
        }

        MMOProfessions.getInstance().getLogger().info("共加载 " + stabilizers.size() + " 个稳定剂");
        return stabilizers;
    }

}