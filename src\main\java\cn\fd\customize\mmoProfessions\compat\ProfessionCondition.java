package cn.fd.customize.mmoProfessions.compat;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.core.Profession;
import cn.fd.customize.mmoProfessions.core.ProfessionData;
import io.lumine.mythic.lib.api.MMOLineConfig;
import net.Indyuce.mmoitems.api.crafting.condition.Condition;
import net.Indyuce.mmoitems.api.player.PlayerData;

/**
 * ProfessionCondition
 *
 * <AUTHOR>
 * @since 2025/7/25 14:34
 */
public class ProfessionCondition extends Condition {

    private final Profession profession;
    private final int level;

    @SuppressWarnings("deprecation")
    public ProfessionCondition(MMOLineConfig config) {
        super("profession");
        config.validate("id");
        this.profession = MMOProfessions.getProfessionManager().getProfession(config.getString("id"));
        this.level = config.getInt("level", 0);
    }

    @Override
    public boolean isMet(PlayerData data) {
        // 职业匹配
        boolean metProfession = MMOProfessions.getProfessionManager().hasProfession(data.getPlayer(),
                this.profession.getId());
        // 等级匹配
        ProfessionData professionData = MMOProfessions.getProfessionManager()
                .getPlayerData(data.getPlayer());
        boolean metLevel = professionData.getLevel(this.profession.getId()) >= this.level;
        // 俩都要匹配
        return metProfession && metLevel;
    }

    @Override
    public String formatDisplay(String string) {
        return string.replace("#profession#", this.profession.getDisplayName())
                .replace("#level#", String.valueOf(this.level));
    }

    @Override
    public void whenCrafting(PlayerData playerData) {
    }

}
