package cn.fd.customize.mmoProfessions.alchemy;

import java.util.ArrayList;
import java.util.List;

import cn.fd.customize.mmoProfessions.compat.ItemSource;

/**
 * 等级选择数据类
 * 表示炼金配方中不同等级对应的输出、时间和成功率
 */
public class LevelChoice {
    private final int requiredLevel;
    private final List<ItemSource> outputs; // 输出列表（带数量）
    private final long duration; // 持续时间（秒）
    private final double baseSuccessRate; // 基础成功率

    /**
     * 构造函数
     * 
     * @param requiredLevel   所需等级
     * @param outputs         输出物品列表（带数量）
     * @param duration        持续时间（秒）
     * @param baseSuccessRate 基础成功率（0.0-1.0）
     */
    public LevelChoice(int requiredLevel, List<ItemSource> outputs, long duration, double baseSuccessRate) {
        this.requiredLevel = requiredLevel;
        this.outputs = new ArrayList<>(outputs);
        this.duration = duration;
        this.baseSuccessRate = baseSuccessRate;
    }

    /**
     * 获取所需等级
     * 
     * @return 所需等级
     */
    public int getRequiredLevel() {
        return requiredLevel;
    }

    /**
     * 获取输出物品列表（带数量）
     * 
     * @return 输出物品列表的副本
     */
    public List<ItemSource> getOutputs() {
        return new ArrayList<>(outputs);
    }

    /**
     * 获取持续时间
     * 
     * @return 持续时间（秒）
     */
    public long getDuration() {
        return duration;
    }

    /**
     * 获取基础成功率
     * 
     * @return 基础成功率（0.0-1.0）
     */
    public double getBaseSuccessRate() {
        return baseSuccessRate;
    }
}
