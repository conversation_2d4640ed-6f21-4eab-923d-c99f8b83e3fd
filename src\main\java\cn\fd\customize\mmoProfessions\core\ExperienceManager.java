package cn.fd.customize.mmoProfessions.core;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 经验管理器 - 负责经验计算、等级计算和经验配置管理
 */
public class ExperienceManager {

    private FileConfiguration expConfig;
    private final Map<String, Map<Integer, Integer>> professionExpRequirements;

    public ExperienceManager() {
        this.professionExpRequirements = new HashMap<>();
        loadExperienceConfig();
    }

    /**
     * 加载经验配置文件
     */
    public void loadExperienceConfig() {
        File expFile = new File(MMOProfessions.getInstance().getDataFolder(), "experience.yml");
        if (!expFile.exists()) {
            MMOProfessions.getInstance().saveResource("experience.yml", false);
        }

        expConfig = YamlConfiguration.loadConfiguration(expFile);
        professionExpRequirements.clear();

        // 加载每个职业的经验需求
        for (String professionId : expConfig.getKeys(false)) {
            ConfigurationSection professionSection = expConfig.getConfigurationSection(professionId);
            if (professionSection != null) {
                ConfigurationSection levelsSection = professionSection.getConfigurationSection("levels");
                if (levelsSection != null) {
                    Map<Integer, Integer> levelExpMap = new HashMap<>();
                    for (String levelStr : levelsSection.getKeys(false)) {
                        try {
                            int level = Integer.parseInt(levelStr);
                            int requiredExp = levelsSection.getInt(levelStr);
                            levelExpMap.put(level, requiredExp);
                        } catch (NumberFormatException ignored) {
                            // 忽略无效配置
                        }
                    }
                    if (!levelExpMap.isEmpty()) {
                        professionExpRequirements.put(professionId, levelExpMap);
                    }
                }
            }
        }
    }

    /**
     * 重新加载经验配置
     */
    public void reloadExperienceConfig() {
        loadExperienceConfig();
    }

    /**
     * 根据经验计算等级
     */
    public int calculateLevel(String professionId, int experience) {
        if (experience < 0)
            return 1;

        Map<Integer, Integer> levelExpMap = professionExpRequirements.get(professionId);
        if (levelExpMap == null) {
            return Math.max(1, (int) Math.sqrt(experience / 50.0) + 1);
        }

        int maxLevel = 1;
        for (Map.Entry<Integer, Integer> entry : levelExpMap.entrySet()) {
            if (experience >= entry.getValue() && entry.getKey() > maxLevel) {
                maxLevel = entry.getKey();
            }
        }
        return maxLevel;
    }

    /**
     * 获取指定职业指定等级所需的经验
     */
    public int getRequiredExp(String professionId, int level) {
        if (level < 1)
            return 0;

        Map<Integer, Integer> levelExpMap = professionExpRequirements.get(professionId);
        if (levelExpMap == null) {
            return level * level * 50;
        }

        return levelExpMap.getOrDefault(level, level * level * 50);
    }

    /**
     * 获取升级到下一级还需要的经验
     */
    public int getExpToNextLevel(String professionId, int currentExp) {
        int currentLevel = calculateLevel(professionId, currentExp);
        int nextLevelExp = getRequiredExp(professionId, currentLevel + 1);
        return Math.max(0, nextLevelExp - currentExp);
    }

    public void addExperience(Player player, String professionId, int amount) {
        Profession profession = MMOProfessions.getProfessionManager().getProfession(professionId);
        if (profession == null)
            return;
        addExperience(player, profession, amount);
    }

    /**
     * 添加经验给玩家
     *
     * 处理流程：
     * 1. 验证职业是否存在
     * 2. 获取玩家当前数据
     * 3. 添加经验值
     * 4. 重新计算等级
     * 5. 如果升级，更新等级并触发升级事件
     * 6. 触发经验获得事件
     *
     * @param player       玩家
     * @param professionId 职业ID
     * @param amount       经验数量
     */
    public void addExperience(Player player, Profession profession, int amount) {
        if (amount <= 0)
            return;

        // 检查玩家是否拥有该职业
        if (!MMOProfessions.getProfessionManager().hasProfession(player, profession.getId())) {
            MMOProfessions.getMessageManager().sendMessage(player, "general.profession_not_granted",
                    profession.getDisplayName());
            return;
        }

        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(player.getUniqueId());
        int currentLevel = data.getLevel(profession.getId());
        int currentExp = data.getExperience(profession.getId());

        // 添加经验
        int newExp = currentExp + amount;
        data.setExperience(profession.getId(), newExp);

        // 重新计算等级
        int newLevel = calculateLevel(profession.getId(), newExp);

        // 检查是否升级
        if (newLevel > currentLevel) {
            profession.onLevelUp(player, newLevel);

            // 升级时立即保存数据
            MMOProfessions.getDataStorage().savePlayerData(player.getUniqueId());
        }

        // 触发经验获得事件
        profession.onExpGain(player, amount);
    }

    /**
     * 获取职业的最大等级
     */
    public int getMaxLevel(String professionId) {
        Map<Integer, Integer> levelExpMap = professionExpRequirements.get(professionId);
        if (levelExpMap == null) {
            return 50; // 默认最大等级
        }
        return levelExpMap.keySet().stream().mapToInt(Integer::intValue).max().orElse(50);
    }

    /**
     * 检查职业是否有经验配置
     */
    public boolean hasProfessionConfig(String professionId) {
        return professionExpRequirements.containsKey(professionId);
    }

    /**
     * 设置玩家职业等级
     */
    public boolean setPlayerLevel(Player player, String professionId, int level) {
        if (level < 1)
            return false;

        ProfessionManager professionManager = MMOProfessions.getProfessionManager();
        if (professionManager.getProfession(professionId) == null)
            return false;
        if (!professionManager.hasProfession(player, professionId))
            return false;

        ProfessionData data = professionManager.getPlayerData(player.getUniqueId());
        data.setExperience(professionId, getRequiredExp(professionId, level));
        return true;
    }

    /**
     * 设置玩家职业经验
     */
    public boolean setExperience(Player player, String professionId, int experience) {
        return setExperience(player, MMOProfessions.getProfessionManager().getProfession(professionId), experience);
    }

    /**
     * 设置玩家职业经验
     */
    public boolean setExperience(Player player, Profession profession, int experience) {
        if (experience < 0)
            return false;
        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(player.getUniqueId());
        data.setExperience(profession.getId(), experience);
        return true;
    }

}
