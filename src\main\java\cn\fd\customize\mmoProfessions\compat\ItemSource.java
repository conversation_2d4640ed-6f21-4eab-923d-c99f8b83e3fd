package cn.fd.customize.mmoProfessions.compat;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * 物品源接口
 * 提供统一的物品处理接口，支持不同的物品源（Vanilla、MMOItems、NeigeItems等）
 */
public interface ItemSource {

    /**
     * 获取物品源工厂
     * 
     * @return 物品源工厂
     */
    ItemSourceFactory getFactory();

    /**
     * 获取配置值
     * 
     * @return 配置中的value值
     */
    String getValue();

    /**
     * 检查给定的物品是否匹配此成分
     * 
     * @param item 要检查的物品
     * @return 是否匹配
     */
    boolean matchesIngredient(ItemStack item);

    /**
     * 生成输出物品
     * 
     * @param player 玩家
     * @return 生成的物品
     */
    ItemStack generateOutput(Player player);

}
