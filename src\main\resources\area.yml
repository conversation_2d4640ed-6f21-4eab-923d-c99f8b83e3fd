# ========================================
# MMOProfessions 区域系统配置文件
# ========================================
#
# 处理逻辑：
#   1. 玩家挖掘 valid 方块时，判断 职业、等级、工具
#   2. 如果都满足，读条 +1 (如 1/5)，然后取消方块破坏事件
#   3. 读条记录在对应区域ID中 (如 minableDiamond)
#   4. 当区域读条进度满时 (5/5)：
#      - 清除 blocks 中的所有方块
#      - 替换为 refreshing_blocks 中定义的方块
#      - 执行 drops (掉落物品、执行命令、给予经验，任意一个均可选)
#   5. 区域进入刷新阶段，此期间方块无法被破坏
#   6. 刷新时间 (refresh_interval) 过后：
#      - 替换 refreshing_blocks 为 blocks
#      - 读条清零，区域恢复可用状态
#
# ========================================

# 钻石矿区域示例
minableDiamond:
  # 区域要求配置
  require:
    profession: mining # 必需职业：挖矿
    level: 10 # 最低等级要求
    tools: # 允许的工具列表
      - source: mmoitems
        value: mmoitem{type=TOOL, id=STEEL_PICKAXE}
        amount: 1 # 可选

  # 区域方块配置
  blocks:
    - world: world # 世界名称
      x: 100 # X坐标
      y: 65 # Y坐标
      z: 200 # Z坐标
      type: DIAMOND_ORE # 方块类型
      valid: true # 是否为有效方块（挖掘此方块才算有效判定）
    - world: world
      x: 101
      y: 65
      z: 200
      type: STONE
      valid: false # 装饰方块，不算有效挖掘
    - world: world
      x: 99
      y: 65
      z: 200
      type: DIAMOND_ORE
      valid: true

  # 读条配置
  max_break_times: 5 # 读条上限（需要挖掘多少次才能完成）

  # 进度条显示配置
  progress_bar:
    # 显示类型配置
    # - title: 在屏幕中央显示大标题
    # - subtitle: 在屏幕中央显示副标题（推荐）
    # - actionbar: 在物品栏上方显示动作栏文本
    type: subtitle

    # 显示文本模板，支持以下占位符：
    # - {0} 或 {current}: 当前进度数值
    # - {1} 或 {max}: 最大进度数值
    # - {percentage}: 百分比进度（如 80.0）
    # - {bar}: 默认可视化进度条（如 ████░░░░）
    # - {bar:长度:满字符:空字符}: 自定义进度条
    #   例如: {bar:10:█:░} = 长度10，满字符█，空字符░
    #   例如: {bar:20:■:□} = 长度20，满字符■，空字符□
    # 支持颜色代码：&a=绿色, &c=红色, &e=黄色, &6=金色, &b=青色等
    text: "&6⛏ 挖掘进度: &e[{current}/{max}] &7{bar:10:█:░}"

    # 更多进度条样式示例：
    # text: "&a进度 {bar:20:■:□} &e{percentage}%"
    # text: "&6[{bar:15:▰:▱}] &e{current}/{max}"
    # text: "&b▶ {bar:12:●:○} &f({current}/{max})"

  # 刷新配置
  refresh_interval: 600 # 刷新间隔（tick）

  # 刷新期间的方块显示
  refreshing_blocks:
    - world: world
      x: 100
      y: 65
      z: 200
      type: DIAMOND_BLOCK # 刷新期间显示的方块类型
    - world: world
      x: 101
      y: 65
      z: 200
      type: STONE
    - world: world
      x: 99
      y: 65
      z: 200
      type: DIAMOND_BLOCK

  # 掉落配置
  drops:
    # 物品掉落
    - item:
        source: mmoitems
        value: mmoitem{type=MATERIAL, id=STEEL_INGOT}
        amount: 2-4 # 掉落数量范围
      chance: 0.8 # 掉落几率 (0.0-1.0)
    # 经验掉落
    - exp: 100 # 经验值
      profession: mining # 职业（必选）
      chance: 1.0 # 掉落几率（可选，默认1.0）
    # 命令执行
    - commands:
        - "give %player% diamond 2"
        - 'tellraw %player% {"text":"你获得了珍贵的钻石！","color":"aqua"}'
      chance: 0.5
