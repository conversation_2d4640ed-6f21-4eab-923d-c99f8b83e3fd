# 消息配置文件
# 支持颜色代码 (&a, &c, &e 等) 和参数替换
# 使用 {0}, {1}, {2} 等表示参数位置

# 通用消息
general:
  no_permission: "&c你没有权限执行此命令！"
  player_not_found: "&c玩家 {0} 不在线！" # {0} = 玩家名
  profession_not_found: "&c职业 {0} 不存在！" # {0} = 职业ID
  profession_not_granted: "&c你没有 {0} 职业！" # {0} = 职业名称
  invalid_number: "&c{0} 不是有效的数字！" # {0} = 输入值

# 经验和等级相关消息
experience:
  gained: "&a+{0} {1}经验" # {0} = 经验数量, {1} = 职业名称
  level_up: "&6★ {0} 等级提升！当前等级: {1}" # {0} = 职业名称, {1} = 新等级

# 职业信息显示
profession_info:
  player_level_header: "&6{0} 的 {1} 信息:" # {0} = 玩家名, {1} = 职业名称
  player_levels_header: "&6{0} 的职业等级:" # {0} = 玩家名
  level: "&e等级: &a{0}" # {0} = 等级
  experience: "&e经验: &a{0}&7/&a{1}" # {0} = 当前经验, {1} = 所需经验
  exp_to_next: "&e升级还需: &a{0} &e经验" # {0} = 还需经验
  profession_line: "&e{0}: &a{1} &7(&a{2}&7)" # {0} = 职业名称, {1} = 等级, {2} = 经验

# 区域相关消息
area:
  refreshing: "&c该区域正在刷新中，请稍后再试！"
  profession_level_required: "&c你需要  {1} 级 {0} 职业才能挖掘此区域！" # {0} = 职业名称,  {1} = 所需等级
  tool_required: "&c你需要使用指定的工具才能挖掘此区域！"

# 炼金系统消息
alchemy:
  # 成功消息
  task_created: "&a炼金任务已创建！队列: {0}, 预计完成时间: {1}" # {0} = 队列编号, {1} = 完成时间
  task_completed: "&a[炼金] 队列 {0} 的炼金任务已完成！" # {0} = 队列编号
  task_failed: "&c[炼金] 队列 {0} 的炼金任务失败了！" # {0} = 队列编号
  task_cancelled: "&c炼金任务已取消"
  queue_unlocked: "&a队列 {0} 已解锁！" # {0} = 队列编号
  system_reloaded: "&a炼金系统已重新加载！"

  # 错误消息
  no_recipe_found: "&c未找到匹配的炼金配方！"
  level_insufficient: "&c炼金等级不足！需要等级: {0}, 当前等级: {1}" # {0} = 需要等级, {1} = 当前等级
  no_profession: "&c你还没有炼金职业！"
  no_input_items: "&c请先放入要炼金的物品！"
  queue_full: "&c队列已满，无法添加更多任务！"
  queue_locked: "&c队列 {0} 尚未解锁！" # {0} = 队列编号
  queue_already_unlocked: "&c队列 {0} 已经解锁了！" # {0} = 队列编号
  queue_unlock_order: "&c只能按顺序解锁队列！下一个可解锁的队列是: {0}" # {0} = 队列编号
  queue_max_reached: "&c已达到最大队列数！"
  task_not_found: "&c未找到指定的任务！"
  cannot_cancel: "&c该任务无法取消！"
  invalid_queue_number: "&c队列编号必须是数字！"

  # 信息消息
  no_tasks: "&e你当前没有任何炼金任务"
  use_command_to_open: "&e使用 /alchemy 命令重新打开炼金台"

  # 状态消息
  status_header: "&6=== 炼金任务状态 ==="
  queue_status: "&e队列 {0}: &a{1}个任务" # {0} = 队列编号, {1} = 任务数量
  task_info: "  &7- {0} {1} (剩余: {2})" # {0} = 任务ID, {1} = 状态, {2} = 剩余时间
  completed_tasks: "&a已完成任务: {0}个 (可在炼金台取出产物)" # {0} = 完成任务数量

  # 统计消息
  stats_header: "&6=== 炼金系统统计 ==="
  stats_total: "&7总任务数: &e{0}" # {0} = 总任务数
  stats_active: "&7活跃任务: &a{0}" # {0} = 活跃任务数
  stats_completed: "&7已完成: &b{0}" # {0} = 已完成任务数
  stats_cancelled: "&7已取消: &c{0}" # {0} = 已取消任务数

  # 命令帮助
  command_usage_queue: "&c用法: /alchemy queue <队列编号>"
  unknown_subcommand: "&c未知的子命令！使用 /alchemy help 查看帮助"
