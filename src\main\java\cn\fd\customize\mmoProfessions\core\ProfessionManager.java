package cn.fd.customize.mmoProfessions.core;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 职业管理器
 */
public class ProfessionManager {

    private final Map<String, Profession> registeredProfessions;
    private final Map<UUID, ProfessionData> playerData;

    public ProfessionManager() {
        this.registeredProfessions = new ConcurrentHashMap<>();
        this.playerData = new ConcurrentHashMap<>();
    }

    /**
     * 注册职业
     */
    public void registerProfession(Profession profession) {
        registeredProfessions.put(profession.getId(), profession);
    }

    /**
     * 获取职业
     */
    public Profession getProfession(String id) {
        Profession profession = registeredProfessions.get(id.toLowerCase());
        if (profession != null)
            return profession;
        else
            throw new IllegalArgumentException("职业ID '" + id + "' 未注册或不存在！");
    }

    /**
     * 获取所有注册的职业
     */
    public Collection<Profession> getAllProfessions() {
        return registeredProfessions.values();
    }

    /**
     * 获取玩家数据
     */
    public ProfessionData getPlayerData(Player player) {
        return getPlayerData(player.getUniqueId());
    }

    /**
     * 获取玩家数据
     */
    public ProfessionData getPlayerData(UUID playerId) {
        return playerData.computeIfAbsent(playerId, uuid -> {
            return MMOProfessions.getDataStorage().loadPlayerData(uuid).getProfessionData(uuid);
        });
    }

    /**
     * 获取所有已加载的玩家ID
     */
    public Set<UUID> getLoadedPlayerIds() {
        return new HashSet<>(playerData.keySet());
    }

    /**
     * 授予职业给玩家
     */
    public void grantProfession(Player player, String professionId) {
        getPlayerData(player).grantProfession(professionId);
    }

    /**
     * 取消授予职业（不删除数据）
     */
    public void revokeProfession(Player player, String professionId) {
        getPlayerData(player).revokeProfession(professionId);
    }

    /**
     * 检查玩家是否拥有指定职业
     */
    public boolean hasProfession(Player player, String professionId) {
        return getPlayerData(player).hasProfession(professionId);
    }

    /**
     * 重置玩家职业数据
     */
    public void resetPlayerData(Player player, String professionId) {
        ProfessionData data = getPlayerData(player);
        if (professionId == null) {
            data.resetAll();
            player.sendMessage("§a所有职业数据已重置！");
        } else {
            data.resetProfession(professionId);
            Profession profession = getProfession(professionId);
            String name = profession != null ? profession.getDisplayName() : professionId;
            player.sendMessage("§a" + name + "职业数据已重置！");
        }
    }

}
