package cn.fd.customize.mmoProfessions.area;

import java.util.function.Consumer;

import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * 区域破坏进度
 */
public class AreaProgress {

    private final String areaId;
    private int current;
    private final int maxBreakTimes;
    private final int refreshInterval;
    private AreaStatus status;

    public AreaProgress(String areaId, int maxBreakTimes, int refreshInterval) {
        this.areaId = areaId;
        this.current = 0;
        this.status = AreaStatus.AVAILABLE;
        this.maxBreakTimes = maxBreakTimes;
        this.refreshInterval = refreshInterval;
    }

    public String getAreaId() {
        return areaId;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int value) {
        this.current = value;
    }

    /**
     * 增加破坏进度
     */
    public void increase() {
        this.current++;
    }

    public AreaStatus getStatus() {
        return status;
    }

    public void setStatus(AreaStatus status) {
        this.status = status;
    }

    public int getMaxBreakTimes() {
        return maxBreakTimes;
    }

    public int getRefreshInterval() {
        return refreshInterval;
    }

    /**
     * 检查是否达到最大破坏次数
     */
    public boolean isBreakComplete() {
        return current >= maxBreakTimes;
    }

    /**
     * 开始刷新阶段 - 严格按照要求的逻辑
     * 区域进入刷新阶段，此期间方块无法被破坏，
     * 刷新时间过后，读条清零，区域恢复可用状态
     */
    public void refresh(JavaPlugin plugin, Runnable callback) {
        this.status = AreaStatus.REFRESHING;
        Bukkit.getScheduler().runTaskLater(plugin, ()->{
            this.status = AreaStatus.AVAILABLE;
            this.current = 0; // 读条清零
            callback.run();
        }, getRefreshInterval());
    }

    /**
     * 区域状态枚举
     */
    public enum AreaStatus {
        AVAILABLE, // 可用状态
        REFRESHING // 刷新状态
    }

}
