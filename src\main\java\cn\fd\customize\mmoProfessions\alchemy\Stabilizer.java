package cn.fd.customize.mmoProfessions.alchemy;

import cn.fd.customize.mmoProfessions.compat.ItemSource;
import cn.fd.customize.mmoProfessions.compat.ItemParser;
import java.util.Map;

import org.bukkit.inventory.ItemStack;

/**
 * 稳定剂效果数据类
 * 表示炼金过程中使用的稳定剂及其效果
 */
public class Stabilizer {
    private final ItemSource ingredient;
    private final double successRateBonus;

    /**
     * 构造函数
     * @param ingredient 稳定剂物品成分
     * @param successRateBonus 成功率加成
     */
    public Stabilizer(ItemSource ingredient, double successRateBonus) {
        this.ingredient = ingredient;
        this.successRateBonus = successRateBonus;
    }

    /**
     * 从字符串构造稳定剂
     * @param ingredient 物品成分字符串
     * @param successRateBonus 成功率加成
     */
    public Stabilizer(Map<String, Object> section, double successRateBonus) {
        this(ItemParser.parseItemSource(section), successRateBonus);
    }

    /**
     * 检查物品是否匹配此稳定剂
     * @param item 要检查的物品
     * @return 是否匹配
     */
    public boolean matches(ItemStack item) {
        return ingredient.matchesIngredient(item);
    }

    /**
     * 获取稳定剂成分
     * @return 稳定剂成分
     */
    public ItemSource getIngredient() {
        return ingredient;
    }

    /**
     * 获取成功率加成
     * @return 成功率加成值
     */
    public double getSuccessRateBonus() {
        return successRateBonus;
    }
}
