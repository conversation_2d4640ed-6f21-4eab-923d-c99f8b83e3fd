package cn.fd.customize.mmoProfessions.professions;

import org.bukkit.entity.Player;

/**
 * 伐木职业
 */
public class LumberingProfession extends AbstractProfession {
    
    public LumberingProfession() {
        super("lumbering", "伐木", "通过砍伐树木获得经验和木材");
    }
    
    @Override
    public void onExpGain(Player player, int amount) {
        super.onExpGain(player, amount);
        // 可以添加伐木特有的经验获得效果
    }
    
    @Override
    public void onLevelUp(Player player, int newLevel) {
        super.onLevelUp(player, newLevel);
        // 特殊升级消息现在通过MessageManager在messages.yml中配置
    }
    
    /**
     * 检查玩家是否可以砍伐指定等级的树木
     */
    public boolean canCut(Player player, int requiredLevel) {
        return hasLevel(player, requiredLevel);
    }
    
    /**
     * 获取砍伐效率加成（基于等级）
     */
    public double getCuttingEfficiency(Player player) {
        int level = getLevel(player);
        return 1.0 + (level * 0.025); // 每级增加2.5%效率
    }
    
    /**
     * 获取最大砍伐方块数（基于等级）
     */
    public int getMaxCutBlocks(Player player) {
        int level = getLevel(player);
        return Math.min(200, 50 + (level * 2)); // 基础50块，每级增加2块，最高200块
    }
    
    /**
     * 获取额外掉落几率（基于等级）
     */
    public double getExtraDropChance(Player player) {
        int level = getLevel(player);
        if (level < 30) return 0.0;
        return Math.min(0.4, (level - 30) * 0.008); // 30级后每级增加0.8%，最高40%
    }
}
