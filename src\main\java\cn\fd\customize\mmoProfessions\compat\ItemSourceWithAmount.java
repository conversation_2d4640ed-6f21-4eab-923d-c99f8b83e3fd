package cn.fd.customize.mmoProfessions.compat;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * 带数量的物品源
 * 包含物品源和数量信息，用于替代原来的generateOutput用法
 */
public class ItemSourceWithAmount {
    
    private final ItemSource itemSource;
    private final String amount;
    
    public ItemSourceWithAmount(ItemSource itemSource, String amount) {
        this.itemSource = itemSource;
        this.amount = amount != null ? amount : "1"; // 默认数量为1
    }
    
    /**
     * 获取物品源
     * @return 物品源
     */
    public ItemSource getItemSource() {
        return itemSource;
    }
    
    /**
     * 获取数量字符串
     * @return 数量字符串
     */
    public String getAmount() {
        return amount;
    }
    
    /**
     * 生成固定数量的输出物品
     * 这个方法替代了原来的generateOutput方法，支持amount参数
     * 
     * @param player 玩家
     * @return 生成的物品，已设置正确的数量
     */
    public ItemStack getFixedOutput(Player player) {
        return itemSource.getFactory().getFixedOutput(itemSource.getValue(), amount, player);
    }
    
    /**
     * 检查给定的物品是否匹配此成分
     * 
     * @param item 要检查的物品
     * @return 是否匹配
     */
    public boolean matchesIngredient(ItemStack item) {
        return itemSource.matchesIngredient(item);
    }
    
    @Override
    public String toString() {
        return String.format("ItemSourceWithAmount{source=%s, amount=%s}", itemSource, amount);
    }
}
