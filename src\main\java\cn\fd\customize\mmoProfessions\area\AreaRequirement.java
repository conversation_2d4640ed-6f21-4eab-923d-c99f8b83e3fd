package cn.fd.customize.mmoProfessions.area;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.core.ProfessionData;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.List;
import cn.fd.customize.mmoProfessions.compat.ItemSource;
import cn.fd.customize.mmoProfessions.compat.ItemParser;
import cn.fd.customize.mmoProfessions.core.Profession;

/**
 * 区域破坏条件
 */
public class AreaRequirement {

    private final Profession profession;
    private final int level;
    private final ItemSource[] tools;

    public AreaRequirement(Profession profession, int level, List<?> tools) {
        this.profession = profession;
        this.level = level;
        this.tools = tools.stream().map(ItemParser::parseItemSource).toArray(ItemSource[]::new);
    }

    public Profession getProfession() {
        return profession;
    }

    public int getLevel() {
        return level;
    }

    public ItemSource[] getTools() {
        return tools;
    }

    /**
     * 检查玩家是否满足要求 - 判断职业、等级、工具
     */
    public boolean satisfy(Player player, ItemStack tool) {
        if (!checkProfessionLevel(player)) {
            MMOProfessions.getMessageManager().sendMessage(player, "area.profession_level_required",
                    getProfession().getDisplayName(), String.valueOf(this.level));
            return false;
        } else if (!checkTool(tool)) {
            MMOProfessions.getMessageManager().sendMessage(player, "area.tool_required");
            return false;
        } else
            return true;
    }

    /**
     * 检查职业等级是否满足要求
     */
    private boolean checkProfessionLevel(Player player) {
        if (getProfession() == null || getLevel() <= 0) {
            return true;
        }
        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(player);
        return data.getLevel(getProfession().getId()) >= getLevel();
    }

    /**
     * 检查工具是否满足要求
     */
    private boolean checkTool(ItemStack tool) {
        if (tool == null || tool.getType().isAir()) {
            return false;
        }
        for (ItemSource allowedTool : getTools()) {
            if (allowedTool.matchesIngredient(tool))
                return true;
        }
        return false;
    }

}
