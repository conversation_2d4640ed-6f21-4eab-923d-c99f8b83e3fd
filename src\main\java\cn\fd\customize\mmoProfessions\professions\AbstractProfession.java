package cn.fd.customize.mmoProfessions.professions;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.core.Profession;
import cn.fd.customize.mmoProfessions.core.ProfessionData;
import org.bukkit.entity.Player;

/**
 * 抽象职业基类
 */
public abstract class AbstractProfession implements Profession {

    protected final String id;
    protected final String displayName;
    protected final String description;
    private static AbstractProfession instance;

    public AbstractProfession(String id, String displayName, String description) {
        this.id = id;
        this.displayName = displayName;
        this.description = description;
        instance = this;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public void onExpGain(Player player, int amount) {
        // 默认实现：发送经验获得消息
        MMOProfessions.getMessageManager().sendMessage(player, "experience.gained", String.valueOf(amount),
                displayName);
    }

    @Override
    public void onLevelUp(Player player, int newLevel) {
        // 默认实现：发送升级消息和音效
        MMOProfessions.getMessageManager().sendMessage(player, "experience.level_up", displayName,
                String.valueOf(newLevel));
        player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
    }

    @Override
    public boolean canUse(Player player) {
        // 默认实现：所有玩家都可以使用
        return true;
    }

    /**
     * 获取玩家当前等级（通过ProfessionManager）
     */
    public int getLevel(Player player) {
        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(player);
        return data.getLevel(this.id);
    }

    /**
     * 获取玩家当前经验（通过ProfessionManager）
     */
    public int getExperience(Player player) {
        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(player);
        return data.getExperience(this.id);
    }

    /**
     * 检查玩家是否达到指定等级
     */
    public boolean hasLevel(Player player, int requiredLevel) {
        return getLevel(player) >= requiredLevel;
    }

    /**
     * 给玩家添加经验
     */
    public void addExperience(Player player, int amount) {
        MMOProfessions.getExperienceManager().addExperience(player, this.id, amount);
    }

    public static AbstractProfession getInstance() {
        return instance;
    }

}
