package cn.fd.customize.mmoProfessions.serialize;

import com.google.gson.*;
import org.bukkit.inventory.ItemStack;

import java.lang.reflect.Type;
import java.util.Map;

/**
 * Gson的ItemStack序列化和反序列化器
 */
public class ItemStackTypeAdapter implements JsonSerializer<ItemStack>, JsonDeserializer<ItemStack> {

    @Override
    public JsonElement serialize(ItemStack src, Type typeOfSrc, JsonSerializationContext context) {
        if (src == null || src.getType().isAir()) {
            return JsonNull.INSTANCE;
        }

        try {
            // 使用Bukkit的序列化方法
            Map<String, Object> serialized = src.serialize();
            return context.serialize(serialized);
        } catch (Exception e) {
            throw new JsonParseException("Failed to serialize ItemStack", e);
        }
    }

    @Override
    public ItemStack deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context)
            throws JsonParseException {
        if (json.isJsonNull()) {
            return null;
        }

        try {
            // 使用Bukkit的反序列化方法
            Map<String, Object> serialized = context.deserialize(json, Map.class);
            return ItemStack.deserialize(serialized);
        } catch (Exception e) {
            throw new JsonParseException("Failed to deserialize ItemStack", e);
        }
    }
}
