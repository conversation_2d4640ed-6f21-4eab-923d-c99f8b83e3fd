package cn.fd.customize.mmoProfessions.compat;

import cn.fd.customize.mmoProfessions.MMOProfessions;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 物品源注册器
 * 管理所有可用的物品源类型，支持动态注册和插件依赖检查
 */
public class ItemSourceRegistry {

    private static final Map<String, ItemSourceFactory> factories = new HashMap<>();

    /**
     * 初始化默认的物品源
     */
    public static void initialize() {
        MMOProfessions.getInstance().getLogger().info("初始化物品源注册器...");

        // 注册原版物品源
        registerFactory(new VanillaItemSourceFactory());

        // 注册MMOItems物品源
        registerFactory(new MMOItemsSourceFactory());

        // 注册NeigeItems物品源
        registerFactory(new NeigeItemsSourceFactory());

        MMOProfessions.getInstance().getLogger().info(
                String.format("物品源注册完成，共注册 %d 个物品源: %s",
                        factories.size(), String.join(", ", factories.keySet())));
    }

    /**
     * 注册物品源工厂
     * 
     * @param factory 物品源工厂
     */
    private static void registerFactory(ItemSourceFactory factory) {
        if (factory.isAvailable()) {
            factories.put(factory.getSourceType(), factory);
        } else {
            MMOProfessions.getInstance().getLogger().warning(
                    String.format("物品源 '%s' 不可用，跳过注册", factory.getSourceType()));
        }
    }

    /**
     * 创建物品源
     * 
     * @param sourceType 物品源类型
     * @param value      物品源值
     * @return 创建的物品源对象
     * @throws IllegalArgumentException 如果物品源类型不支持
     */
    public static ItemSource createItemSource(String sourceType, String value) {
        sourceType = sourceType.toLowerCase();

        ItemSourceFactory factory = factories.get(sourceType);
        if (factory == null) {
            throw new IllegalArgumentException("不支持的物品源类型: " + sourceType);
        }

        try {
            ItemSource source = factory.create(value);
            MMOProfessions.getInstance().getLogger().fine(
                    String.format("创建物品源: %s -> %s", sourceType, value));
            return source;
        } catch (Exception e) {
            MMOProfessions.getInstance().getLogger().severe(
                    String.format("创建物品源失败: %s -> %s, 错误: %s", sourceType, value, e.getMessage()));
            throw new RuntimeException("创建物品源失败: " + sourceType, e);
        }
    }

    /**
     * 检查物品源类型是否可用
     * 
     * @param sourceType 物品源类型
     * @return 是否可用
     */
    public static boolean isSourceAvailable(String sourceType) {
        sourceType = sourceType.toLowerCase();
        ItemSourceFactory factory = factories.get(sourceType);
        return factory != null && factory.isAvailable();
    }

    /**
     * 获取所有注册的物品源类型
     * 
     * @return 物品源类型集合
     */
    public static Set<String> getRegisteredSources() {
        return factories.keySet();
    }

}
