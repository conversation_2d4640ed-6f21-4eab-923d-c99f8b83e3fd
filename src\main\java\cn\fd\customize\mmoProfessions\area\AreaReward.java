package cn.fd.customize.mmoProfessions.area;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.compat.ItemParser;
import cn.fd.customize.mmoProfessions.compat.ItemSource;
import cn.fd.customize.mmoProfessions.compat.ItemSourceFactory;
import cn.fd.customize.mmoProfessions.core.Profession;
import io.lumine.mythic.lib.api.util.ItemFactory;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.List;

/**
 * 区域奖励
 */
public abstract class AreaReward {

    protected final double chance;
    protected final MMOProfessions plugin;

    public AreaReward(double chance) {
        this.chance = chance;
        this.plugin = MMOProfessions.getInstance();
    }

    public double getChance() {
        return chance;
    }

    /**
     * 检查是否应该触发奖励（基于几率）
     */
    public boolean shouldGrant() {
        return Math.random() < chance;
    }

    /**
     * 给予奖励给玩家
     */
    public abstract void give(Player player);

    /**
     * 物品奖励子类
     */
    public static class ItemReward extends AreaReward {
        private final ItemSource item;
        private final String amount;

        public ItemReward(Object itemValue, double chance, String amount) {
            super(chance);
            this.item = ItemParser.parseItemSource(itemValue);
            this.amount = amount;
        }

        @Override
        public void give(Player player) {
            if (!shouldGrant()) {
                return;
            }
            try {
                // 获取物品
                ItemStack itemStack = item.generateOutput(player);
                // 随机数量设置
                int amount = ItemSourceFactory.parseAmount(this.amount);
                if (amount >= 0) {
                    itemStack.setAmount(amount);
                    if (amount >= 1) {
                        player.getInventory().addItem(itemStack);
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("给予物品奖励失败: " + e.getMessage());
            }
        }

    }

    /**
     * 经验奖励子类
     */
    public static class ExpReward extends AreaReward {
        private final int expValue;
        private final Profession profession;

        public ExpReward(int expValue, String profession, double chance) {
            super(chance);
            this.expValue = expValue;
            this.profession = plugin.getProfessionManager().getProfession(profession);
        }

        @Override
        public void give(Player player) {
            if (!shouldGrant()) {
                return;
            }
            try {
                plugin.getExperienceManager().addExperience(player, getProfession(), expValue);
            } catch (Exception e) {
                plugin.getLogger().warning("给予经验奖励失败: " + e.getMessage());
            }
        }

        public int getExpValue() {
            return expValue;
        }

        public Profession getProfession() {
            return profession;
        }

    }

    /**
     * 命令奖励子类
     */
    public static class CommandReward extends AreaReward {
        private final List<String> commands;

        public CommandReward(List<String> commands, double chance) {
            super(chance);
            this.commands = commands;
        }

        @Override
        public void give(Player player) {
            if (!shouldGrant()) {
                return;
            }

            try {
                if (commands != null && !commands.isEmpty()) {
                    for (String command : commands) {
                        String processedCommand = command.replace("%player%", player.getName());
                        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("执行命令奖励失败: " + e.getMessage());
            }
        }

        public List<String> getCommands() {
            return commands;
        }
    }

}
