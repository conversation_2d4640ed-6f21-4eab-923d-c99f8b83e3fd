package cn.fd.customize.mmoProfessions.area;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;

/**
 * 区域方块数据类
 */
public class AreaBlock {

    private final Location location;
    private final Material configuredType;
    private final boolean valid;

    public AreaBlock(Location location, Material type, boolean valid) {
        this.location = location;
        this.configuredType = type;
        this.valid = valid;
    }

    public AreaBlock(World world, int x, int y, int z, Material type, boolean valid) {
        this(new Location(world, x, y, z), type, valid);
    }

    /**
     * 是否为有效方块
     */
    public boolean isValid() {
        return valid;
    }

    /**
     * 获取Location对象
     */
    public Location getLocation() {
        return location;
    }

    /**
     * 获取Block实例
     */
    public Block getBlock() {
        return location.getBlock();
    }

    /**
     * 将方块放入世界
     */
    public void putInWorld() {
        location.getBlock().setType(configuredType);
    }

    /**
     * 检查位置是否匹配
     */
    public boolean matches(Location otherLocation) {
        return getLocation().equals(otherLocation);
    }

    /**
     * 检查方块是否为空气
     */
    public boolean isAir() {
        return getLocation().getBlock().getType() == Material.AIR;
    }

    /**
     * 将方块设置为空气（清除方块）
     */
    public void setToAir() {
        getLocation().getBlock().setType(Material.AIR);
    }

    /**
     * 检查方块是否已加载
     */
    public boolean isLoaded() {
        return getLocation().getBlock().getChunk().isLoaded();
    }

    @Override
    public boolean equals(Object obj) {
        return getBlock().equals(obj);
    }

}
