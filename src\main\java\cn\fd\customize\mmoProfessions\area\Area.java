package cn.fd.customize.mmoProfessions.area;

import java.util.List;
import org.bukkit.Location;

/**
 * 区域
 */
public class Area {

    private final String id;
    private final AreaRequirement requirement;
    private final AreaProgress progress;
    private final List<AreaBlock> blocks;
    private final List<AreaBlock> refreshingBlocks;
    private final ProgressBar progressBar;
    private final List<AreaReward> rewards;

    public Area(String id, AreaRequirement requirement, AreaProgress progress, List<AreaBlock> blocks,
            List<AreaBlock> refreshingBlocks, ProgressBar progressBar, List<AreaReward> rewards) {
        this.id = id;
        this.requirement = requirement;
        this.progress = progress;
        this.blocks = blocks;
        this.refreshingBlocks = refreshingBlocks;
        this.progressBar = progressBar;
        this.rewards = rewards;
        // 初始化方块
        for (AreaBlock block : this.blocks) {
            block.putInWorld();
        }
    }

    public String getId() {
        return id;
    }

    public AreaRequirement getRequirement() {
        return requirement;
    }

    public AreaProgress getProgress() {
        return progress;
    }

    public List<AreaBlock> getBlocks() {
        return blocks;
    }

    public List<AreaBlock> getRefreshingBlocks() {
        return refreshingBlocks;
    }

    public ProgressBar getProgressBar() {
        return progressBar;
    }

    public List<AreaReward> getRewards() {
        return rewards;
    }

    /**
     * 查找指定位置的任意方块（包括无效方块）
     */
    public AreaBlock findBlock(Location location) {
        if (blocks == null)
            return null;

        for (AreaBlock block : blocks) {
            if (block.matches(location)) {
                return block;
            }
        }
        return null;
    }

    /**
     * 检查位置是否属于此区域
     */
    public boolean isInArea(Location location) {
        return findBlock(location) != null;
    }

    /**
     * 查找指定位置所属的区域（静态方法，供其他类复用）
     * 
     * @param location    位置
     * @param areaManager 区域管理器
     * @return 区域对象，如果不在任何区域内则返回null
     */
    public static Area findAreaByLocation(Location location, Iterable<Area> areas) {
        if (areas == null || location == null) {
            return null;
        }
        for (Area area : areas) {
            if (area.isInArea(location)) {
                return area;
            }
        }
        return null;
    }

}
