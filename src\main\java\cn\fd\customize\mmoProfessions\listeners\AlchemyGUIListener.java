package cn.fd.customize.mmoProfessions.listeners;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.alchemy.AlchemyMainGUI;
// import cn.fd.customize.mmoProfessions.alchemy.AlchemyQueueGUI;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;

/**
 * 炼金GUI事件监听器
 * 处理炼金界面的所有交互事件
 */
public class AlchemyGUIListener implements Listener {

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // 检查是否是炼金主界面
        AlchemyMainGUI mainGUI = MMOProfessions.getAlchemyManager().getMainGUI();
        if (mainGUI.isAlchemyMainGUI(player, event.getInventory())) {
            mainGUI.handleInventoryClick(event);
            return;
        }

        // // 检查是否是炼金队列界面
        // AlchemyQueueGUI queueGUI = MMOProfessions.getAlchemyManager().getQueueGUI();
        // if (queueGUI.isAlchemyQueueGUI(player, event.getInventory())) {
        //     queueGUI.handleInventoryClick(event);
        //     return;
        // }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();

        // 检查是否是炼金主界面
        AlchemyMainGUI mainGUI = MMOProfessions.getAlchemyManager().getMainGUI();
        if (mainGUI.isAlchemyMainGUI(player, event.getInventory())) {
            mainGUI.handleInventoryClose(event);
            return;
        }

        // // 检查是否是炼金队列界面
        // AlchemyQueueGUI queueGUI = MMOProfessions.getAlchemyManager().getQueueGUI();
        // if (queueGUI.isAlchemyQueueGUI(player, event.getInventory())) {
        //     queueGUI.handleInventoryClose(event);
        //     return;
        // }
    }

    @EventHandler
    public void onInventoryDrag(InventoryDragEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // 检查是否是炼金主界面
        AlchemyMainGUI mainGUI = MMOProfessions.getAlchemyManager().getMainGUI();
        if (mainGUI.isAlchemyMainGUI(player, event.getInventory())) {
            mainGUI.handleInventoryDrag(event);
            return;
        }

        // // 检查是否是炼金队列界面
        // AlchemyQueueGUI queueGUI = MMOProfessions.getAlchemyManager().getQueueGUI();
        // if (queueGUI.isAlchemyQueueGUI(player, event.getInventory())) {
        //     queueGUI.handleInventoryDrag(event);
        //     return;
        // }
    }
}
