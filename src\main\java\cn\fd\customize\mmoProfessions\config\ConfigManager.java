package cn.fd.customize.mmoProfessions.config;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.configuration.file.FileConfiguration;

/**
 * 配置文件管理类
 * 负责加载和管理所有配置项
 */
public class ConfigManager {

    private FileConfiguration config;

    public ConfigManager() {
        loadConfig();
    }

    /**
     * 加载配置文件
     */
    public void loadConfig() {
        MMOProfessions.getInstance().saveDefaultConfig();
        MMOProfessions.getInstance().reloadConfig();
        config = MMOProfessions.getInstance().getConfig();

        MMOProfessions.getInstance().getLogger().info("配置文件已加载！");
    }

    /**
     * 重新加载配置文件
     */
    public void reloadConfig() {
        loadConfig();
    }

    /**
     * 获取职业条件显示的正面文本
     */
    public String getProfessionConditionPositiveDisplay() {
        return config.getConfigurationSection("profession").getConfigurationSection("condition_display").getString("positive", "&a✔ Required Profession: #profession#");
    }

    /**
     * 获取职业条件显示的负面文本
     */
    public String getProfessionConditionNegativeDisplay() {
        return config.getConfigurationSection("profession").getConfigurationSection("condition_display").getString("negative", "&c✖ Required Profession: #profession#");
    }

    /**
     * 获取配置文件对象
     */
    public FileConfiguration getConfig() {
        return config;
    }

}
