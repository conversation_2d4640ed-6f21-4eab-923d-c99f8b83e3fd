package cn.fd.customize.mmoProfessions.professions;

import org.bukkit.entity.Player;

/**
 * 挖矿职业
 */
public class MiningProfession extends AbstractProfession {
    
    public MiningProfession() {
        super("mining", "挖矿", "通过挖掘矿物获得经验和材料");
    }
    
    @Override
    public void onExpGain(Player player, int amount) {
        super.onExpGain(player, amount);
        // 可以添加挖矿特有的经验获得效果
    }
    
    @Override
    public void onLevelUp(Player player, int newLevel) {
        super.onLevelUp(player, newLevel);
        // 特殊升级消息现在通过MessageManager在messages.yml中配置
    }
    
    /**
     * 检查玩家是否可以挖掘指定等级的矿物
     */
    public boolean canMine(Player player, int requiredLevel) {
        return hasLevel(player, requiredLevel);
    }
    
    /**
     * 获取挖掘效率加成（基于等级）
     */
    public double getMiningEfficiency(Player player) {
        int level = getLevel(player);
        return 1.0 + (level * 0.02); // 每级增加2%效率
    }
    
    /**
     * 获取额外掉落几率（基于等级）
     */
    public double getExtraDropChance(Player player) {
        int level = getLevel(player);
        if (level < 30) return 0.0;
        return Math.min(0.5, (level - 30) * 0.01); // 30级后每级增加1%，最高50%
    }
}
