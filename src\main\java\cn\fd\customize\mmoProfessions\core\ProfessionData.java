package cn.fd.customize.mmoProfessions.core;

import cn.fd.customize.mmoProfessions.MMOProfessions;

import java.util.*;

/**
 * 玩家职业数据类
 *
 * 用于存储和管理单个玩家的所有职业相关数据，包括：
 * - 各职业的经验值
 * - 各职业的授予状态
 * - 等级通过经验动态计算
 */
public class ProfessionData {

    /** 玩家的唯一标识符 */
    private final UUID playerId;

    /** 存储各职业的经验值，键为职业ID，值为经验值 */
    private final Map<String, Integer> experience;

    /** 存储玩家已授予的职业列表 */
    private final List<String> grantedProfessions;

    ProfessionData(UUID playerId, Map<String, Integer> experience, List<String> grantedProfessions) {
        this.playerId = playerId;
        this.experience = experience;
        this.grantedProfessions = grantedProfessions;
    }

    /**
     * 获取玩家ID
     * 
     * @return 玩家的UUID
     */
    public UUID getPlayerId() {
        return playerId;
    }

    /**
     * 获取指定职业的经验值
     * 
     * @param professionId 职业ID
     * @return 经验值，如果职业不存在则返回0
     */
    public int getExperience(String professionId) {
        return experience.getOrDefault(professionId, 0);
    }

    /**
     * 设置指定职业的经验值
     *
     * @param professionId 职业ID
     * @param exp          经验值，不能小于0
     */
    public void setExperience(String professionId, int exp) {
        experience.put(professionId, Math.max(0, exp));
        saveData();
    }

    /**
     * 为指定职业增加经验值
     * 
     * @param professionId 职业ID
     * @param exp          要增加的经验值
     */
    public void addExperience(String professionId, int exp) {
        setExperience(professionId, getExperience(professionId) + exp);
    }

    /**
     * 获取指定职业的等级（动态计算）
     *
     * @param professionId 职业ID
     * @return 等级，如果职业不存在则返回1（默认等级）
     */
    public int getLevel(String professionId) {
        int exp = getExperience(professionId);
        if (exp == 0) {
            return 1;
        }

        // 通过ExperienceManager计算等级
        ExperienceManager expManager = MMOProfessions.getInstance().getExperienceManager();
        return expManager.calculateLevel(professionId, exp);
    }

    /**
     * 获取所有职业的经验值副本
     * 
     * @return 包含所有职业经验值的Map副本
     */
    public Map<String, Integer> getAllExperience() {
        return new HashMap<>(experience);
    }

    /**
     * 获取所有职业的等级副本（动态计算）
     *
     * @return 包含所有职业等级的Map副本
     */
    public Map<String, Integer> getAllLevels() {
        Map<String, Integer> levels = new HashMap<>();
        for (String professionId : experience.keySet()) {
            levels.put(professionId, getLevel(professionId));
        }
        return levels;
    }

    /**
     * 重置指定职业的数据
     * 将指定职业的经验重置为0（等级会自动变为1）
     *
     * @param professionId 要重置的职业ID
     */
    public void resetProfession(String professionId) {
        experience.put(professionId, 0);
        saveData(); // 重置数据时立即保存
    }

    /**
     * 重置所有职业数据
     * 清空所有职业的经验数据（等级会自动重置）
     */
    public void resetAll() {
        experience.clear();
        saveData(); // 重置所有数据时立即保存
    }

    /**
     * 授予职业给玩家
     *
     * @param professionId 职业ID
     */
    public void grantProfession(String professionId) {
        if (!grantedProfessions.contains(professionId)) {
            grantedProfessions.add(professionId);
        }
        // 如果是第一次授予，设置初始经验
        if (!experience.containsKey(professionId)) {
            experience.put(professionId, 0);
        }
        saveData(); // 授予职业时立即保存
    }

    /**
     * 取消授予职业（不删除数据）
     *
     * @param professionId 职业ID
     */
    public void revokeProfession(String professionId) {
        grantedProfessions.remove(professionId);
        saveData(); // 撤销职业时立即保存
    }

    /**
     * 检查玩家是否拥有指定职业
     *
     * @param professionId 职业ID
     * @return 是否拥有该职业
     */
    public boolean hasProfession(String professionId) {
        return grantedProfessions.contains(professionId);
    }

    /**
     * 获取所有已授予的职业ID
     *
     * @return 已授予职业ID的集合
     */
    public Set<String> getGrantedProfessions() {
        return new HashSet<>(grantedProfessions);
    }

    /**
     * 保存职业数据
     */
    private void saveData() {
        MMOProfessions.getDataStorage().savePlayerData(playerId);
    }

}
