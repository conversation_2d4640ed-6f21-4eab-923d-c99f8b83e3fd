package cn.fd.customize.mmoProfessions.listeners;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;

/**
 * 区域方块破坏监听器
 */
public class AreaBlockBreakListener implements Listener {

    public AreaBlockBreakListener() {
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBreak(BlockBreakEvent event) {
        if (event.isCancelled()) {
            return;
        }

        // 处理区域方块破坏
        boolean shouldCancel = MMOProfessions.getInstance().getAreaManager()
                .handleBlockBreak(event.getPlayer(), event.getBlock().getLocation());

        if (shouldCancel) {
            event.setCancelled(true);
        }
    }
}
