package cn.fd.customize.mmoProfessions.commands;

import cn.fd.customize.mmoProfessions.MMOProfessions;
import cn.fd.customize.mmoProfessions.area.AreaProgress;
import cn.fd.customize.mmoProfessions.area.Area;
import cn.fd.customize.mmoProfessions.core.ProfessionData;
import cn.fd.customize.mmoProfessions.core.Profession;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 职业命令处理器
 */
public class ProfessionCommand implements CommandExecutor, TabCompleter {

    public ProfessionCommand() {
    }


    /**
     * 获取在线玩家
     */
    private Player getOnlinePlayer(CommandSender sender, String playerName) {
        Player player = Bukkit.getPlayer(playerName);
        if (player == null) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.player_not_found", playerName);
        }
        return player;
    }

    /**
     * 获取职业（支持通过ID或显示名称查找）
     */
    private Profession getProfession(CommandSender sender, String professionIdOrName) {
        // 首先尝试通过ID查找
        Profession profession = MMOProfessions.getProfessionManager().getProfession(professionIdOrName);

        // 如果通过ID找不到，尝试通过显示名称查找
        if (profession == null) {
            for (Profession p : MMOProfessions.getProfessionManager().getAllProfessions()) {
                if (p.getDisplayName().equalsIgnoreCase(professionIdOrName)) {
                    profession = p;
                    break;
                }
            }
        }

        if (profession == null) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.profession_not_found", professionIdOrName);
        }
        return profession;
    }

    /**
     * 解析整数
     */
    private Integer parseInteger(CommandSender sender, String value) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.invalid_number", value);
            return null;
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 检查管理员权限
        if (!sender.hasPermission("mmoprofessions.admin")) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.no_permission");
            return true;
        }

        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        try {
            switch (subCommand) {
                case "reload":
                    return handleReload(sender);
                case "level":
                case "lv":
                    return handleLevel(sender, args);
                case "exp":
                case "experience":
                    return handleExp(sender, args);
                case "reset":
                    return handleReset(sender, args);
                case "grant":
                    return handleGrant(sender, args);
                case "revoke":
                    return handleRevoke(sender, args);
                case "info":
                    return handleInfo(sender, args);
                case "area":
                    return handleArea(sender, args);
                default:
                    sendHelp(sender);
                    return true;
            }
        } catch (Exception e) {
            sender.sendMessage("§c命令执行出错: " + e.getMessage());
            MMOProfessions.getInstance().getLogger().warning("命令执行异常: " + e.getMessage());
            return true;
        }
    }

    /**
     * 处理重载命令
     */
    private boolean handleReload(CommandSender sender) {
        MMOProfessions.getInstance().reloadAllConfigs();
        sender.sendMessage("§a配置文件已重新加载！");
        return true;
    }

    /**
     * 处理等级命令
     * /mmop level [set/view] [玩家] [职业] (value)
     */
    private boolean handleLevel(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§c用法: /mmop level [set/view] [玩家] [职业] (value)");
            return true;
        }

        String action = args[1].toLowerCase();

        switch (action) {
            case "view":
                return handleLevelView(sender, args);
            case "set":
                return handleLevelSet(sender, args);
            default:
                sender.sendMessage("§c用法: /mmop level [set/view] [玩家] [职业] (value)");
                return true;
        }
    }

    /**
     * 处理等级查看
     * /mmop level view [玩家] [职业]
     */
    private boolean handleLevelView(CommandSender sender, String[] args) {
        Player target;
        String professionId = null;

        if (args.length == 2) {
            // /mmop level view - 查看自己所有职业等级
            if (!(sender instanceof Player)) {
                sender.sendMessage("§c控制台必须指定玩家名！");
                return true;
            }
            target = (Player) sender;
        } else if (args.length == 3) {
            // /mmop level view <玩家> - 查看指定玩家所有职业等级
            target = Bukkit.getPlayer(args[2]);
            if (target == null) {
                MMOProfessions.getMessageManager().sendMessage(sender, "general.player_not_found", args[2]);
                return true;
            }
        } else if (args.length == 4) {
            // /mmop level view <玩家> <职业> - 查看指定玩家指定职业等级
            target = Bukkit.getPlayer(args[2]);
            if (target == null) {
                MMOProfessions.getMessageManager().sendMessage(sender, "general.player_not_found", args[2]);
                return true;
            }
            professionId = args[3];
        } else {
            sender.sendMessage("§c用法: /mmop level view [玩家] [职业]");
            return true;
        }

        showLevels(sender, target, professionId);
        return true;
    }

    /**
     * 处理等级设置
     * /mmop level set <玩家> <职业> <等级>
     */
    private boolean handleLevelSet(CommandSender sender, String[] args) {
        if (args.length != 5) {
            sender.sendMessage("§c用法: /mmop level set <玩家> <职业> <等级>");
            return true;
        }

        Player target = Bukkit.getPlayer(args[2]);
        if (target == null) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.player_not_found", args[2]);
            return true;
        }

        String professionId = args[3];
        Profession profession = MMOProfessions.getProfessionManager().getProfession(professionId);
        if (profession == null) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.profession_not_found", professionId);
            return true;
        }

        try {
            int level = Integer.parseInt(args[4]);
            if (level <= 0) {
                sender.sendMessage("§c等级必须大于0！");
                return true;
            }

            boolean success = MMOProfessions.getExperienceManager().setPlayerLevel(target, professionId, level);
            if (success) {
                sender.sendMessage(
                        "§a已将 " + target.getName() + " 的 " + profession.getDisplayName() + " 等级设置为 " + level + "！");
            } else {
                sender.sendMessage("§c设置等级失败！");
            }
        } catch (NumberFormatException e) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.invalid_number", args[4]);
        }

        return true;
    }

    /**
     * 显示等级信息
     */
    private void showLevels(CommandSender sender, Player target, String professionId) {
        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(target);

        if (professionId != null) {
            // 显示指定职业
            Profession profession = MMOProfessions.getProfessionManager().getProfession(professionId);
            if (profession == null) {
                MMOProfessions.getMessageManager().sendMessage(sender, "general.profession_not_found", professionId);
                return;
            }

            // 检查玩家是否拥有该职业
            if (!data.hasProfession(professionId)) {
                MMOProfessions.getMessageManager().sendMessage(sender, "general.profession_not_granted",
                        profession.getDisplayName());
                return;
            }

            int level = data.getLevel(professionId);
            int exp = data.getExperience(professionId);
            int nextLevelExp = MMOProfessions.getExperienceManager().getRequiredExp(professionId, level + 1);
            int expToNext = MMOProfessions.getExperienceManager().getExpToNextLevel(professionId, exp);

            MMOProfessions.getMessageManager().sendMessage(sender, "profession_info.player_level_header",
                    target.getName(),
                    profession.getDisplayName());
            MMOProfessions.getMessageManager().sendMessage(sender, "profession_info.level", String.valueOf(level));
            MMOProfessions.getMessageManager().sendMessage(sender, "profession_info.experience", String.valueOf(exp),
                    String.valueOf(nextLevelExp));
            MMOProfessions.getMessageManager().sendMessage(sender, "profession_info.exp_to_next",
                    String.valueOf(expToNext));
        } else {
            // 显示所有已授予的职业
            MMOProfessions.getMessageManager().sendMessage(sender, "profession_info.player_levels_header",
                    target.getName());
            for (Profession profession : MMOProfessions.getProfessionManager().getAllProfessions()) {
                if (data.hasProfession(profession.getId())) {
                    int level = data.getLevel(profession.getId());
                    int exp = data.getExperience(profession.getId());
                    MMOProfessions.getMessageManager().sendMessage(sender, "profession_info.profession_line",
                            profession.getDisplayName(), String.valueOf(level), String.valueOf(exp));
                }
            }
        }
    }

    /**
     * 处理经验命令
     * /mmop exp [set|view|add] <玩家> <职业> (value)
     */
    private boolean handleExp(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§c用法: /mmop exp [set|view|add] <玩家> <职业> (value)");
            return true;
        }

        String action = args[1].toLowerCase();

        switch (action) {
            case "view":
                return handleExpView(sender, args);
            case "set":
                return handleExpSet(sender, args);
            case "add":
                return handleExpAdd(sender, args);
            default:
                sender.sendMessage("§c用法: /mmop exp [set|view|add] <玩家> <职业> (value)");
                return true;
        }
    }

    /**
     * 处理经验查看
     * /mmop exp view <玩家> <职业>
     */
    private boolean handleExpView(CommandSender sender, String[] args) {
        // 复用等级查看逻辑，因为showLevels同时显示等级和经验
        String[] newArgs = new String[args.length];
        newArgs[0] = args[0]; // "mmop"
        newArgs[1] = "view";
        for (int i = 2; i < args.length; i++) {
            newArgs[i] = args[i];
        }
        return handleLevelView(sender, newArgs);
    }

    /**
     * 处理经验设置
     * /mmop exp set <玩家> <职业> <经验>
     */
    private boolean handleExpSet(CommandSender sender, String[] args) {
        if (args.length != 5) {
            sender.sendMessage("§c用法: /mmop exp set <玩家> <职业> <经验>");
            return true;
        }

        Player target = getOnlinePlayer(sender, args[2]);
        if (target == null)
            return true;

        Profession profession = getProfession(sender, args[3]);
        if (profession == null)
            return true;

        Integer experience = parseInteger(sender, args[4]);
        if (experience == null || experience < 0) {
            sender.sendMessage("§c经验值不能小于0！");
            return true;
        }

        boolean success = MMOProfessions.getExperienceManager().setExperience(target, args[3], experience);
        if (success) {
            sender.sendMessage("§a已将 " + target.getName() + " 的 " + profession.getDisplayName() +
                    " 经验设置为 " + experience + "！");
        } else {
            sender.sendMessage("§c设置经验失败！");
        }

        return true;
    }

    /**
     * 处理经验添加
     * /mmop exp add <玩家> <职业> <经验>
     */
    private boolean handleExpAdd(CommandSender sender, String[] args) {
        if (args.length != 5) {
            sender.sendMessage("§c用法: /mmop exp add <玩家> <职业> <经验>");
            return true;
        }

        Player target = getOnlinePlayer(sender, args[2]);
        if (target == null)
            return true;

        Profession profession = getProfession(sender, args[3]);
        if (profession == null)
            return true;

        // 检查玩家是否拥有该职业
        if (!MMOProfessions.getProfessionManager().hasProfession(target, args[3])) {
            sender.sendMessage("§c玩家 " + target.getName() + " 没有 " + profession.getDisplayName() + " 职业！");
            return true;
        }

        Integer amount = parseInteger(sender, args[4]);
        if (amount == null || amount <= 0) {
            sender.sendMessage("§c经验值必须大于0！");
            return true;
        }

        // 获取当前数据
        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(target);
        int currentExp = data.getExperience(args[3]);
        int currentLevel = data.getLevel(args[3]);

        // 添加经验
        MMOProfessions.getExperienceManager().addExperience(target, args[3], amount);

        // 获取新数据并发送消息
        int newLevel = data.getLevel(args[3]);
        int newExp = data.getExperience(args[3]);

        String message = "§a已为 " + target.getName() + " 的 " + profession.getDisplayName() +
                " 添加 " + amount + " 经验！";

        if (newLevel > currentLevel) {
            message += "(等级: " + currentLevel + " → " + newLevel + ", 经验: " + currentExp + " → " + newExp + ")";
        } else {
            message += "(等级: " + newLevel + ", 经验: " + currentExp + " → " + newExp + ")";
        }

        sender.sendMessage(message);
        return true;
    }

    /**
     * 处理重置命令
     */
    private boolean handleReset(CommandSender sender, String[] args) {
        if (args.length < 2 || args.length > 3) {
            sender.sendMessage("§c用法: /mmop reset <玩家> [职业]");
            return true;
        }

        Player target = Bukkit.getPlayer(args[1]);
        if (target == null) {
            MMOProfessions.getMessageManager().sendMessage(sender, "general.player_not_found", args[1]);
            return true;
        }

        String professionId = args.length == 3 ? args[2] : null;

        if (professionId != null) {
            Profession profession = MMOProfessions.getProfessionManager().getProfession(professionId);
            if (profession == null) {
                MMOProfessions.getMessageManager().sendMessage(sender, "general.profession_not_found", professionId);
                return true;
            }
        }

        MMOProfessions.getProfessionManager().resetPlayerData(target, professionId);

        if (professionId != null) {
            sender.sendMessage("§a已重置 " + target.getName() + " 的 " + professionId + " 职业数据！");
        } else {
            sender.sendMessage("§a已重置 " + target.getName() + " 的所有职业数据！");
        }

        return true;
    }

    /**
     * 处理职业信息命令 - 查看自身职业信息
     * /mmop info
     */
    private boolean handleInfo(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;
        ProfessionData data = MMOProfessions.getProfessionManager().getPlayerData(player);

        sender.sendMessage("§6=== 你的职业信息 ===");
        boolean hasAnyProfession = false;

        for (Profession profession : MMOProfessions.getProfessionManager().getAllProfessions()) {
            if (data.hasProfession(profession.getId())) {
                hasAnyProfession = true;
                int level = data.getLevel(profession.getId());
                int exp = data.getExperience(profession.getId());
                int nextLevelExp = MMOProfessions.getExperienceManager().getRequiredExp(profession.getId(), level + 1);
                int expToNext = MMOProfessions.getExperienceManager().getExpToNextLevel(profession.getId(), exp);

                sender.sendMessage("§e" + profession.getDisplayName() + ":");
                sender.sendMessage("  §7等级: §a" + level);
                sender.sendMessage("  §7经验: §a" + exp + "§7/§a" + nextLevelExp);
                sender.sendMessage("  §7升级还需: §a" + expToNext + " §7经验");
            }
        }

        if (!hasAnyProfession) {
            sender.sendMessage("§c你还没有任何职业！");
        }

        return true;
    }

    /**
     * 处理授予职业命令
     */
    private boolean handleGrant(CommandSender sender, String[] args) {
        if (args.length != 3) {
            sender.sendMessage("§c用法: /mmop grant <玩家> <职业>");
            return true;
        }

        Player target = getOnlinePlayer(sender, args[1]);
        if (target == null)
            return true;

        Profession profession = getProfession(sender, args[2]);
        if (profession == null)
            return true;

        MMOProfessions.getProfessionManager().grantProfession(target, args[2]);
        sender.sendMessage("§a已为 " + target.getName() + " 授予职业: " + profession.getDisplayName());
        return true;
    }

    /**
     * 处理取消授予职业命令
     */
    private boolean handleRevoke(CommandSender sender, String[] args) {
        if (args.length != 3) {
            sender.sendMessage("§c用法: /mmop revoke <玩家> <职业>");
            return true;
        }

        Player target = getOnlinePlayer(sender, args[1]);
        if (target == null)
            return true;

        Profession profession = getProfession(sender, args[2]);
        if (profession == null)
            return true;

        MMOProfessions.getProfessionManager().revokeProfession(target, args[2]);
        sender.sendMessage("§a已取消 " + target.getName() + " 的职业: " + profession.getDisplayName() + " (数据保留)");
        return true;
    }

    /**
     * 处理区域命令
     * /mmop area [list|info|reload] (区域ID)
     */
    private boolean handleArea(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§c用法: /mmop area [list|info|reload] (区域ID)");
            return true;
        }

        String subCommand = args[1].toLowerCase();
        switch (subCommand) {
            case "list":
                return handleAreaList(sender);
            case "info":
                return handleAreaInfo(sender, args);
            case "reload":
                return handleAreaReload(sender);
            default:
                sender.sendMessage("§c未知的区域子命令: " + subCommand);
                return true;
        }
    }

    /**
     * 处理区域列表命令
     */
    private boolean handleAreaList(CommandSender sender) {
        Map<String, Area> areas = MMOProfessions.getAreaManager().getAreas();
        if (areas.isEmpty()) {
            sender.sendMessage("§e当前没有配置任何区域");
            return true;
        }

        sender.sendMessage("§6=== 区域列表 ===");
        for (Area area : areas.values()) {
            String status = area.getProgress().getStatus() == AreaProgress.AreaStatus.REFRESHING
                    ? "§c刷新中"
                    : "§a可用";

            sender.sendMessage("§e" + area.getId() + " §7- " + status + " §7(职业: "
                    + area.getRequirement().getProfession().getDisplayName() + ", 等级: "
                    + area.getRequirement().getLevel() + ")");
        }
        return true;
    }

    /**
     * 处理区域信息命令
     */
    private boolean handleAreaInfo(CommandSender sender, String[] args) {
        if (args.length < 3) {
            sender.sendMessage("§c用法: /mmop area info <区域ID>");
            return true;
        }

        String areaId = args[2];
        Area area = MMOProfessions.getAreaManager().getArea(areaId);
        if (area == null) {
            sender.sendMessage("§c区域 " + areaId + " 不存在");
            return true;
        }

        AreaProgress progress = area.getProgress();
        sender.sendMessage("§6=== 区域信息: " + areaId + " ===");
        sender.sendMessage("§e职业要求: §a" + area.getRequirement().getProfession().getDisplayName());
        sender.sendMessage("§e等级要求: §a" + area.getRequirement().getLevel());
        sender.sendMessage("§e最大破坏次数: §a" + progress.getMaxBreakTimes());
        sender.sendMessage("§e刷新间隔: §a" + progress.getRefreshInterval());

        if (progress != null) {
            sender.sendMessage("§e当前状态: "
                    + (progress.getStatus() == AreaProgress.AreaStatus.REFRESHING
                            ? "§c刷新中"
                            : "§a可用"));
            sender.sendMessage("§e当前进度: §a" + progress.getCurrent() + "/" + progress.getMaxBreakTimes());
        }

        sender.sendMessage("§e方块数量: §a" + area.getBlocks().size());
        sender.sendMessage("§e奖励数量: §a" + (area.getRewards() != null ? area.getRewards().size() : 0));
        return true;
    }

    /**
     * 处理区域重载命令
     */
    private boolean handleAreaReload(CommandSender sender) {
        MMOProfessions.getAreaManager().reloadAreas();
        sender.sendMessage("§a区域配置已重新加载");
        return true;
    }

    /**
     * 发送帮助信息
     */
    private void sendHelp(CommandSender sender) {
        sender.sendMessage("§6=== MMO职业系统命令 ===");
        sender.sendMessage("§e/mmop reload §7- 重新加载配置文件");
        sender.sendMessage("§e/mmop level [set|view] <玩家> <职业> (value) §7- 等级操作");
        sender.sendMessage("§e/mmop exp [set|view|add] <玩家> <职业> (value) §7- 经验操作");
        sender.sendMessage("§e/mmop reset <玩家> (职业) §7- 重置数据");
        sender.sendMessage("§e/mmop grant <玩家> <职业> §7- 授予职业");
        sender.sendMessage("§e/mmop revoke <玩家> <职业> §7- 取消授予职业");
        sender.sendMessage("§e/mmop info §7- 查看自身职业信息");
        sender.sendMessage("§e/mmop area [list|info|reload] (区域ID) §7- 区域管理");
    }

    // ==================== Tab补全辅助方法 ====================

    private List<String> filterStartsWith(List<String> list, String prefix) {
        return list.stream()
                .filter(item -> item.toLowerCase().startsWith(prefix.toLowerCase()))
                .collect(Collectors.toList());
    }

    private List<String> getOnlinePlayerNames(String prefix) {
        return Bukkit.getOnlinePlayers().stream()
                .map(Player::getName)
                .filter(name -> name.toLowerCase().startsWith(prefix.toLowerCase()))
                .collect(Collectors.toList());
    }

    private List<String> getProfessionIds(String prefix) {
        return MMOProfessions.getProfessionManager().getAllProfessions().stream()
                .map(Profession::getId)
                .filter(id -> id.toLowerCase().startsWith(prefix.toLowerCase()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (args.length == 1) {
            return filterStartsWith(Arrays.asList("reload", "level", "exp", "reset", "grant", "revoke", "info", "area"),
                    args[0]);
        }

        String subCommand = args[0].toLowerCase();

        if (args.length == 2) {
            switch (subCommand) {
                case "level":
                    return filterStartsWith(Arrays.asList("set", "view"), args[1]);
                case "exp":
                    return filterStartsWith(Arrays.asList("set", "view", "add"), args[1]);
                case "reset":
                case "grant":
                case "revoke":
                    return getOnlinePlayerNames(args[1]);
                case "area":
                    return filterStartsWith(Arrays.asList("list", "info", "reload"), args[1]);
            }
        } else if (args.length == 3) {
            switch (subCommand) {
                case "level":
                case "exp":
                    return getOnlinePlayerNames(args[2]);
                case "reset":
                case "grant":
                case "revoke":
                    return getProfessionIds(args[2]);
                case "area":
                    if ("info".equals(args[1])) {
                        return getAreaIds(args[2]);
                    }
                    break;
            }
        } else if (args.length == 4 && (subCommand.equals("level") || subCommand.equals("exp"))) {
            return getProfessionIds(args[3]);
        }

        return new ArrayList<>();
    }

    /**
     * 获取区域ID列表
     */
    private List<String> getAreaIds(String prefix) {
        return MMOProfessions.getAreaManager().getAreas().keySet().stream()
                .filter(id -> id.toLowerCase().startsWith(prefix.toLowerCase()))
                .collect(Collectors.toList());
    }

}
